/* global $ */
import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import moment from 'moment';
import { isEqual } from 'lodash';
import withTranslations from '../../utils/Translations/withTranslations';
import translatePropTypes from '../../../translatePropTypes';
import Icon from '../../utils/Icon';
import FormControlErrorMessage from '../../utils/FormControlErrorMessage';
import FormControlInfoMessage from '../../utils/FormControlInfoMessage';
import {
  WEEK_DAYS,
  MAX_YEARS_LIMIT,
  MomentDateFormat,
  MomentDateTimeFormat,
  TransportFormat,
  TransportDateTimeFormat,
  format,
  formatDateTimeLocal,
  getDurationString,
} from '../../../utils/dateTime';

import makeCalendarLocale from '../../../utils/makeCalendarLocale';
import withUniqueId from '../../../utils/withUniqueId';
import AnimatedTitle from './AnimatedTitle';
import withUserPreferences from '../../../data/withUserPreferences';
import { getLanguage } from '../../utils/Translations/helpers/makeTranslationHelpers';

@withUserPreferences()
@withTranslations
@withUniqueId()
export default class DateRangePicker extends React.PureComponent {
  static propTypes = {
    name: PropTypes.string,
    animateTitle: PropTypes.bool,
    isDisabled: PropTypes.bool,
    placeholder: PropTypes.string,
    isRequired: PropTypes.bool,
    errorMessage: PropTypes.string,
    onStartDateChange: PropTypes.func,
    onEndDateChange: PropTypes.func,
    onChange: PropTypes.func,
    startDate: PropTypes.string,
    endDate: PropTypes.string,
    className: PropTypes.string,
    hasTimePicker: PropTypes.bool,
    hasRange: PropTypes.bool,
    customRange: PropTypes.arrayOf(
      PropTypes.shape({
        label: PropTypes.string,
        range: PropTypes.array,
      }),
    ),
    minDate: PropTypes.string,
    maxDate: PropTypes.string,
    isFutureDatesDisabled: PropTypes.bool,
    isPastDatesDisabled: PropTypes.bool,
    formatString: PropTypes.string,
    hasDurationMessage: PropTypes.bool,
    durationFormat: PropTypes.oneOf(['days', 'weeks', 'months', 'years']),
    hasSelfCheck: PropTypes.bool,
    isOnOverlay: PropTypes.bool,
    ...withUniqueId.props,
    ...translatePropTypes,
  };

  static defaultProps = {
    name: null,
    startDate: '',
    endDate: '',
    animateTitle: true,
    placeholder: '',
    isDisabled: false,
    isRequired: false,
    errorMessage: null,
    onStartDateChange: null,
    onEndDateChange: null,
    onChange: null,
    hasTimePicker: false,
    className: '',
    hasRange: false,
    customRange: null,
    minDate: undefined,
    maxDate: undefined,
    formatString: undefined,
    hasDurationMessage: true,
    durationFormat: 'days',
    hasSelfCheck: true,
    isFutureDatesDisabled: false,
    isPastDatesDisabled: false,
    isOnOverlay: false,
  };

  componentDidMount() {
    this.$daterangepicker = $(this.daterangepicker);
    this.createPicker();
  }

  componentDidUpdate(prevProps) {
    //probably a language change is triggered
    if (!isEqual(prevProps.me, this.props.me)) {
      this.destroyPicker();
      this.$daterangepicker = $(this.daterangepicker);
      this.createPicker();
    }
  }

  componentWillUnmount() {
    this.destroyPicker();
  }

  handleChange = ([startDate, endDate]) => {
    const {
      onStartDateChange,
      onEndDateChange,
      hasTimePicker,
      onChange,
      preferences,
    } = this.props;
    const transportFormat = hasTimePicker
      ? TransportDateTimeFormat
      : TransportFormat;

    const language = getLanguage(preferences);

    const send = {
      startDate:
        startDate && formatDateTimeLocal(startDate, language, transportFormat),
      endDate:
        endDate && formatDateTimeLocal(endDate, language, transportFormat),
    };

    if (onStartDateChange && startDate) {
      onStartDateChange(send.startDate);
    }

    if (onEndDateChange && endDate) {
      onEndDateChange(send.endDate);
    }

    if (onChange && startDate && endDate) {
      onChange(send.startDate, send.endDate);
    }
  };

  handlePreventDefault = e => e.preventDefault();

  onRef = el => {
    this.daterangepicker = el;
  };

  destroyPicker = () => {
    this.$daterangepicker && this.$daterangepicker.calendar('destroy');
  };

  createPicker = () => {
    const {
      hasTimePicker,
      hasRange,
      customRange,
      hasSelfCheck,
      isFutureDatesDisabled,
      isPastDatesDisabled,
      t,
      isOnOverlay,
    } = this.props;
    let { minDate, maxDate } = this.props;
    const format = this.formatString;

    const today = setMidnight(moment());

    minDate =
      (minDate && moment(minDate).toDate()) ||
      (isPastDatesDisabled && today.clone().toDate()) ||
      undefined;
    maxDate = (
      (maxDate && moment(maxDate)) ||
      (isFutureDatesDisabled && today) ||
      today.clone().add(MAX_YEARS_LIMIT, 'years')
    ).toDate();

    const yesterday = today.clone().subtract(1, 'days');
    const startOfWeek = today.clone().startOf('week');
    const endOfWeek = today.clone().endOf('week');

    this.$daterangepicker.calendar({
      id: this.id,
      clockpicker: hasTimePicker,
      cancelButtonText: t(isOnOverlay ? 'Go Back' : 'Cancel'),
      applyButtonText: t('Apply'),
      errorText:
        hasSelfCheck && t('Start Time must be before or equal to End Time'),
      onDone: this.handleChange,
      hasRange,
      minDate,
      maxDate,
      format,
      moment,
      locale: 'locale',
      locales: {
        locale: makeCalendarLocale(t),
      },
      customRange:
        // eslint-disable-next-line no-nested-ternary
        minDate && maxDate
          ? today.isSameOrAfter(minDate) && today.isSameOrBefore(maxDate)
            ? customRange || [
                {
                  label: t('Today'),
                  range: [today.toDate(), today.toDate()],
                },
                {
                  label: t('Yesterday'),
                  range: [yesterday.toDate(), yesterday.toDate()],
                },
                {
                  label: t('This Week'),
                  range: [startOfWeek.toDate(), endOfWeek.toDate()],
                },
                {
                  label: t('This Month'),
                  range: [
                    today.startOf('month').toDate(),
                    today.endOf('month').toDate(),
                  ],
                },
                {
                  label: t('This Year'),
                  range: [
                    today.startOf('year').toDate(),
                    today.endOf('year').toDate(),
                  ],
                },
              ]
            : []
          : customRange || [
              {
                label: t('Today'),
                range: [today.toDate(), today.toDate()],
              },
              {
                label: t('Yesterday'),
                range: [yesterday.toDate(), yesterday.toDate()],
              },
              {
                label: t('This Week'),
                range: [startOfWeek.toDate(), endOfWeek.toDate()],
              },
              {
                label: t('This Month'),
                range: [
                  today.startOf('month').toDate(),
                  today.endOf('month').toDate(),
                ],
              },
              {
                label: t('This Year'),
                range: [
                  today.startOf('year').toDate(),
                  today.endOf('year').toDate(),
                ],
              },
            ],
    });
  };

  renderAnimatedTitle = () => {
    const { placeholder, isRequired, startDate, endDate } = this.props;

    return (
      <AnimatedTitle
        isRequired={isRequired}
        placeholder={placeholder}
        value={placeholder && startDate && endDate}
      />
    );
  };

  renderErrorMessage = () => {
    const { errorMessage } = this.props;
    return <FormControlErrorMessage errorMessage={errorMessage} />;
  };

  makeDurationMessage = () => {
    const { durationFormat, startDate, endDate, t } = this.props;

    return (
      <FormControlInfoMessage
        message={getDurationString(durationFormat, startDate, endDate, t)}
        messageAlign="right"
      />
    );
  };

  handleOpenPicker = () => {
    const { isDisabled } = this.props;
    if (!isDisabled) {
      this.$daterangepicker.trigger('click');
    }
  };

  get formatString() {
    if (this.props.formatString) {
      return this.props.formatString;
    }
    return this.props.hasTimePicker ? MomentDateTimeFormat : MomentDateFormat;
  }

  get id() {
    return this.props.uid + this.props.currentLanguage;
  }

  render() {
    const {
      className,
      errorMessage,
      name,
      animateTitle,
      hasDurationMessage,
      placeholder,
      isRequired,
      startDate,
      endDate,
      isDisabled,
    } = this.props;

    const value =
      startDate && endDate
        ? `${format(startDate, this.formatString)} - ${format(
            endDate,
            this.formatString,
          )}`
        : '';

    return (
      <div className={classnames('form-group form-group-material', className)}>
        {animateTitle && this.renderAnimatedTitle()}
        <div className="input-group">
          <input
            key={`input_${this.id}`}
            ref={this.onRef}
            autoComplete="off"
            className="form-control daterange-from-to"
            disabled={isDisabled}
            id={`input_${name}`}
            name={name}
            placeholder={`${placeholder}${isRequired ? ' *' : ''}`}
            title={value}
            value={value}
            onChange={this.handlePreventDefault}
          />
          <span
            className="input-group-addon cursor-pointer"
            onClick={this.handleOpenPicker}
          >
            <Icon name="calendar2" />
          </span>
        </div>
        {errorMessage && this.renderErrorMessage()}
        {hasDurationMessage && !errorMessage && this.makeDurationMessage()}
      </div>
    );
  }
}

function setMidnight(date) {
  return date.set({ hour: 0, minute: 0, second: 0, millisecond: 0 });
}
