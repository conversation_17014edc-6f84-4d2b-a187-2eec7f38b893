.text-editor-label {
  font-weight: 500;
}
.fr-popup {
  z-index: 1200px !important;
}
.text-editor {
  word-break: break-word;
  word-wrap: anywhere;
  :global(button[id^='imageManager-']) {
    display: none !important;
  }

  :global(.fr-view img) {
    max-width: 100%;
    height: auto !important;
  }
  :global(.fr-box),
  :global(.fr-box .fr-second-toolbar),
  :global(.fr-box .fr-toolbar.fr-top),
  :global(.fr-box .fr-wrapper) {
    border-radius: 0 !important;
  }

  :global(.fr-box) {
    :global(.fr-second-toolbar),
    :global(.fr-bottom) {
      border: none;
    }

    :global(.fr-counter) {
      display: none !important;
    }
    :global(.fr-svg) {
      width: 18px !important;
      height: 18px !important;
    }
    :global(.fr-floating-btn) {
      height: 26px;
      width: 26px;
    }
  }
}

.editor-disabled {
  :global(.fr-element.fr-view) {
    padding: 0 !important;
  }

  :global(.fr-wrapper) {
    box-shadow: none !important;
    border-bottom: 1px solid #eee !important;
    height: 100% !important;
  }

  :global(.fr-element) {
    min-height: auto !important;
  }

  :global(.fr-bottom.fr-disabled) {
    display: none;
  }

  :global(.fr-counter) {
    display: none;
  }
}

.toolbar-hidden {
  button[data-cmd='quote'],
  button[data-cmd='fontFamily'],
  button[data-cmd='fullscreen'],
  button[data-cmd='inlineStyle'],
  button[data-cmd='paragraphStyle'],
  button[data-cmd='specialCharacters'],
  button[data-cmd='selectAll'],
  button[data-cmd='print'],
  button[data-cmd='help'],
  button[data-cmd='html'],
  button[data-cmd='undo'],
  button[data-cmd='insertHR'],
  button[data-cmd='redo'] {
    display: none;
  }
}

.toolbar-visible {
  button[data-cmd='quote'],
  button[data-cmd='fontFamily'],
  button[data-cmd='fullscreen'],
  button[data-cmd='inlineStyle'],
  button[data-cmd='paragraphStyle'],
  button[data-cmd='specialCharacters'],
  button[data-cmd='selectAll'],
  button[data-cmd='print'],
  button[data-cmd='help'],
  button[data-cmd='html'],
  button[data-cmd='undo'],
  button[data-cmd='insertHR'],
  button[data-cmd='redo'] {
    display: block;
  }
}

.liftedError {
  position: relative;
  z-index: 1;
}
.customCounter {
  float: right;
  color: #999999;
  min-height: 20px;
}
.rtl {
  direction: rtl !important;
}

:global(.fr-toolbar.fr-btn-grp) {
  margin: 0 !important;
}
:global(.fr-btn-grp.fr-float-left) {
  margin: 0 !important;
}
:global(.fr-btn-grp.fr-float-right) {
  margin: 0 !important;
}

.text-editor {
  :global(button[data-cmd^='fileAttachment']) {
    background-color: #f0f0f0 !important;
    height: auto !important;
    color: #2196f3 !important;
    padding: 3px 0 !important;
    border-radius: 50px !important;
    margin-top: 8px !important;
    margin-right: 10px !important;
    i {
      margin: 4px 3px !important;
    }
  }
  :global(button[data-cmd^='send']) {
    color: #2296f3 !important;
    border-left: 1px solid #e1e1e1 !important;
    border-radius: 0 !important;
    padding-left: 5px !important;
  }
  :global(button[data-cmd^='cancel']) {
    color: #999999 !important;
    border-left: 1px solid #e1e1e1 !important;
    border-radius: 0 !important;
    padding-left: 5px !important;
  }
  :global(button[data-cmd^='edit']) {
    color: #2296f3 !important;
    border-radius: 0 !important;
  }
  :global(.fr-toolbar.fr-desktop.fr-top.fr-basic) {
    padding-left: 5px !important;
    padding-right: 5px !important;
  }
}


.m3 {
  margin: 3px !important;
}