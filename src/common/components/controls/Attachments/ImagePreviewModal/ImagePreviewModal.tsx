import React, { FC, useState, useEffect, useCallback, useMemo } from 'react';
import classNames from 'classnames';

import { IFileAttachmentTemp } from '../../../../abstract/IFileAttachments';
import Modal from '../../../utils/Modal';
import IconButton from '../../IconButton';
import useT from '../../../utils/Translations/useT';
import styles from './ImagePreviewModal.scss';

const ZOOM_FACTOR = 1.5;
const ZOOM_IN_SPEED = 5;
const ZOOM_OUT_SPEED = 0.5;

interface IImagePreviewModal {
  isVisible: boolean;
  images: IFileAttachmentTemp[];
  currentImageIndex: number;
  onClose: () => void;
  onImageChange: (index: number) => void;
}

const ImagePreviewModal: FC<IImagePreviewModal> = ({
  isVisible,
  images,
  currentImageIndex,
  onClose,
  onImageChange,
}) => {
  const t = useT();
  const [zoomLevel, setZoomLevel] = useState(1);
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const currentImage = useMemo(() => images[currentImageIndex], [
    images,
    currentImageIndex,
  ]);

  const canNavigatePrevious = useMemo(() => currentImageIndex > 0, [
    currentImageIndex,
  ]);

  const canNavigateNext = useMemo(() => currentImageIndex < images.length - 1, [
    currentImageIndex,
    images.length,
  ]);

  // Reset zoom and position when image changes
  useEffect(() => {
    setZoomLevel(1);
    setImagePosition({ x: 0, y: 0 });
  }, [currentImageIndex]);

  const handlePrevious = useCallback(() => {
    if (canNavigatePrevious) {
      onImageChange(currentImageIndex - 1);
    }
  }, [canNavigatePrevious, currentImageIndex, onImageChange]);

  const handleNext = useCallback(() => {
    if (canNavigateNext) {
      onImageChange(currentImageIndex + 1);
    }
  }, [canNavigateNext, currentImageIndex, onImageChange]);

  const handleZoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(prev * ZOOM_FACTOR, ZOOM_IN_SPEED));
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoomLevel(prev => Math.max(prev / ZOOM_FACTOR, ZOOM_OUT_SPEED));
  }, []);

  const handleResetZoom = useCallback(() => {
    setZoomLevel(1);
    setImagePosition({ x: 0, y: 0 });
  }, []);

  const handleDownload = useCallback(() => {
    if (currentImage?.url) {
      const link = document.createElement('a');
      link.href = currentImage.url;
      link.download = currentImage.fileName || 'image';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }, [currentImage]);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (zoomLevel > 1) {
        setIsDragging(true);
        setDragStart({
          x: e.clientX - imagePosition.x,
          y: e.clientY - imagePosition.y,
        });
      }
    },
    [zoomLevel, imagePosition],
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (isDragging && zoomLevel > 1) {
        setImagePosition({
          x: e.clientX - dragStart.x,
          y: e.clientY - dragStart.y,
        });
      }
    },
    [isDragging, zoomLevel, dragStart],
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isVisible) return;

      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          handlePrevious();
          break;
        case 'ArrowRight':
          e.preventDefault();
          handleNext();
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
        case '+':
        case '=':
          e.preventDefault();
          handleZoomIn();
          break;
        case '-':
          e.preventDefault();
          handleZoomOut();
          break;
        case '0':
          e.preventDefault();
          handleResetZoom();
          break;
        default:
          break;
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }

    return undefined;
  }, [
    isVisible,
    handlePrevious,
    handleNext,
    onClose,
    handleZoomIn,
    handleZoomOut,
    handleResetZoom,
  ]);

  if (!currentImage) {
    return null;
  }

  return (
    <Modal
      isOverlayClickHidesModal
      className={styles.imagePreviewModal}
      isCloseCrossVisible={false}
      size="xl"
      visible={isVisible}
      onClose={onClose}
    >
      <div className={styles.modalContent}>
        {/* Header with controls */}
        <div className={styles.header}>
          <div className={styles.leftControls}>
            <IconButton
              hasBackground={false}
              iconClassName="text-white"
              iconName="arrow-left12"
              title={t('Back')}
              onClick={onClose}
            />
            <span className={styles.imageTitle}>{currentImage.fileName}</span>
          </div>

          <div className={styles.rightControls}>
            {/* <IconButton
              hasBackground={false}
              iconClassName="text-white"
              iconName="search"
              title={t('Zoom to fit')}
              onClick={handleResetZoom}
            /> */}
            <IconButton
              className="ml-10 mr-10"
              hasBackground={false}
              iconClassName="text-white"
              iconName="zoom-in"
              title={t('Zoom in')}
              onClick={handleZoomIn}
            />
            <IconButton
              hasBackground={false}
              iconClassName="text-white"
              iconName="zoom-out"
              title={t('Zoom out')}
              onClick={handleZoomOut}
            />
            <span className={styles.separator}>|</span>
            <IconButton
              hasBackground={false}
              iconClassName="text-white"
              iconName="download5"
              title={t('Download')}
              onClick={handleDownload}
            />
          </div>
        </div>

        {/* Main image area */}
        <div className={styles.imageContainer}>
          {/* Navigation arrows */}
          {canNavigatePrevious && (
            <button
              className={classNames(styles.navButton, styles.navButtonLeft)}
              title={t('Previous image')}
              type="button"
              onClick={handlePrevious}
            >
              <i className="icon-arrow-left2" />
            </button>
          )}

          {canNavigateNext && (
            <button
              className={classNames(styles.navButton, styles.navButtonRight)}
              title={t('Next image')}
              type="button"
              onClick={handleNext}
            >
              <i className="icon-arrow-right2" />
            </button>
          )}

          {/* Main image */}
          <div
            className={styles.imageWrapper}
            onMouseDown={handleMouseDown}
            onMouseLeave={handleMouseUp}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
          >
            <img
              alt={currentImage.fileName}
              className={styles.mainImage}
              draggable={false}
              src={currentImage.url}
              // eslint-disable-next-line react/forbid-dom-props
              style={{
                transform: `scale(${zoomLevel}) translate(${
                  imagePosition.x / zoomLevel
                }px, ${imagePosition.y / zoomLevel}px)`,
                cursor:
                  // eslint-disable-next-line no-nested-ternary
                  zoomLevel > 1
                    ? isDragging
                      ? 'grabbing'
                      : 'grab'
                    : 'default',
              }}
            />
          </div>
        </div>

        {/* Thumbnail strip */}
        {images.length > 1 && (
          <div className={styles.thumbnailStrip}>
            <div className={styles.thumbnailContainer}>
              {images.map((image, index) => {
                const _onImageChange = () => {
                  onImageChange(index);
                };
                return (
                  <button
                    key={image.fileId || image.uploadToken || index}
                    className={classNames(styles.thumbnail, {
                      [styles.thumbnailActive]: index === currentImageIndex,
                    })}
                    title={image.fileName}
                    type="button"
                    onClick={_onImageChange}
                  >
                    <img
                      alt={image.fileName}
                      className={styles.thumbnailImage}
                      src={image.url}
                    />
                  </button>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ImagePreviewModal;
