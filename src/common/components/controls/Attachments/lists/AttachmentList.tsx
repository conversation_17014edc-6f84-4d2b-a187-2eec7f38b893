import React, { FC, useMemo, useState, useCallback } from 'react';
import classnames from 'classnames';
import { filter } from 'lodash';

import { IFileAttachmentTemp } from '../../../../abstract/IFileAttachments';

import { TDropdownActions } from '../../Dropdown';
import IAttachmentItemComponentProps from '../abstract/IAttachmentItemComponentProps';
import AttachmentItem from '../listItems/AttachmentItem';
import ImageGrid from '../ImageGrid';
import ImagePreviewModal from '../ImagePreviewModal';
import styles from './AttachmentList.scss';

const AttachmentList: FC<IAttachmentListProps> = ({
  isDownloadable = false,
  onRemove,
  children,
  attachmentPreviewComponent,
  hasLargeIcons,
  editable,
  attachments,
  attachmentItemComponent,
  onChange,
  dropdownOptions,
  maxDescriptionLength,
  hideIcons,
  hasLowStrike,
  listClassName,
  onSplitAudio,
  onImageAiImprovement,
  allowedNumberOfFiles,
  isThreadAttachment,
  showAttachmentNameOnly,
}) => {
  const AttachmentItemComponent = attachmentItemComponent || AttachmentItem;

  const [isImagePreviewOpen, setIsImagePreviewOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Callback functions for image preview
  const handleImageClick = useCallback((index: number) => {
    setCurrentImageIndex(index);
    setIsImagePreviewOpen(true);
  }, []);

  const handleCloseImagePreview = useCallback(() => {
    setIsImagePreviewOpen(false);
  }, []);

  const handleImageChange = useCallback((index: number) => {
    setCurrentImageIndex(index);
  }, []);

  const hasSplitAudio = useMemo(
    () => allowedNumberOfFiles && allowedNumberOfFiles > attachments.length,
    [allowedNumberOfFiles, attachments],
  );

  const imageAttachments = useMemo(
    () =>
      filter(attachments, attachment => attachment.mimeType?.includes('image')),
    [attachments],
  );

  const nonImageAttachments = useMemo(
    () =>
      filter(
        attachments,
        attachment => !attachment.mimeType?.includes('image'),
      ),
    [attachments],
  );

  if (isThreadAttachment && !editable) {
    return (
      <div className="component-attachment-files">
        <div className="attachments-container">
          {imageAttachments.length > 0 && (
            <ImageGrid
              className={styles.threadImageGrid}
              images={imageAttachments}
              onImageClick={handleImageClick}
            />
          )}

          {nonImageAttachments.length > 0 && (
            <ul
              className={classnames(
                'attached-area',
                'attached-area-overide',
                styles.attachmentContainer,
                listClassName,
              )}
            >
              {nonImageAttachments.map((attachment, index) => (
                <AttachmentItemComponent
                  key={attachment.fileId || attachment.uploadToken}
                  attachment={attachment}
                  attachmentPreviewComponent={attachmentPreviewComponent}
                  dropdownOptions={
                    dropdownOptions ? dropdownOptions(attachment) : []
                  }
                  editable={editable}
                  hasLargeIcons={hasLargeIcons}
                  hasLowStrike={hasLowStrike}
                  hideIcons={hideIcons}
                  index={index}
                  isDownloadable={isDownloadable}
                  isThreadAttachment={isThreadAttachment}
                  maxDescriptionLength={maxDescriptionLength}
                  showAttachmentNameOnly={showAttachmentNameOnly}
                  onChange={onChange}
                  onImageAiImprovement={onImageAiImprovement}
                  onRemove={onRemove}
                  onSplitAudio={hasSplitAudio ? onSplitAudio : undefined}
                />
              ))}
            </ul>
          )}

          <ImagePreviewModal
            currentImageIndex={currentImageIndex}
            images={imageAttachments}
            isVisible={isImagePreviewOpen}
            onClose={handleCloseImagePreview}
            onImageChange={handleImageChange}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="component-attachment-files">
      <div className="attachments-container">
        <ul
          className={classnames(
            'attached-area',
            'attached-area-overide',
            styles.attachmentContainer,
            listClassName,
          )}
        >
          {(isThreadAttachment && !editable
            ? nonImageAttachments
            : attachments
          ).map((attachment, index) => (
            <AttachmentItemComponent
              key={attachment.fileId || attachment.uploadToken}
              attachment={attachment}
              attachmentPreviewComponent={attachmentPreviewComponent}
              dropdownOptions={
                dropdownOptions ? dropdownOptions(attachment) : []
              }
              editable={editable}
              hasLargeIcons={hasLargeIcons}
              hasLowStrike={hasLowStrike}
              hideIcons={hideIcons}
              index={index}
              isDownloadable={isDownloadable}
              isThreadAttachment={isThreadAttachment}
              maxDescriptionLength={maxDescriptionLength}
              showAttachmentNameOnly={showAttachmentNameOnly}
              onChange={onChange}
              onImageAiImprovement={onImageAiImprovement}
              onRemove={onRemove}
              onSplitAudio={hasSplitAudio ? onSplitAudio : undefined}
            />
          ))}
          {children}
        </ul>
      </div>
    </div>
  );
};

export default AttachmentList;

export interface IAttachmentListProps {
  editable: boolean;
  onRemove?: Function;
  attachments: IFileAttachmentTemp[];
  isDownloadable?: boolean;
  attachmentPreviewComponent?: React.ComponentType;
  hasLargeIcons?: boolean;
  attachmentItemComponent?: React.ComponentType<IAttachmentItemComponentProps>;
  dropdownOptions?: (data: IFileAttachmentTemp) => TDropdownActions[];
  onChange?: (attachments: IFileAttachmentTemp) => void;
  maxDescriptionLength?: number;
  hideIcons?: boolean;
  hasLowStrike?: boolean;
  listClassName?: string;
  onSplitAudio?: (
    attachment: IFileAttachmentTemp,
    splittedFiles: string[],
  ) => Promise<void>;
  onImageAiImprovement?: (
    attachment: IFileAttachmentTemp,
    image: string,
  ) => Promise<void>;
  allowedNumberOfFiles?: number;
  isThreadAttachment?: boolean;
  showAttachmentNameOnly?: boolean;
}
