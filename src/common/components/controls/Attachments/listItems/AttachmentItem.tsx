import classnames from 'classnames';
import React, { FC, useCallback, useMemo, useRef, useState } from 'react';
import uuid from 'uuid';
import { IFileAttachmentTemp } from '../../../../abstract/IFileAttachments';
import Icon from '../../../utils/Icon';
import AttachmentPreview from '../AttachmentPreview';

import styles from './AttachmentItem.scss';

const AttachmentItem: FC<IAttachmentItemProps> = ({
  isDownloadable = false,
  hideIcons = false,
  hasLowStrike = true,
  onRemove = null,
  attachmentPreviewComponent = null,
  attachment,
  editable,
  isThreadAttachment,
  showAttachmentNameOnly,
}) => {
  const [isHover, setIsHover] = useState<boolean>(false);
  const [isPreview, setIsPreview] = useState<boolean>(false);

  const elId = useRef(`tooltip-${attachment.fileId || uuid.v4()}`).current;

  const handleOnClick = useCallback(() => {
    setIsPreview(true);
  }, []);

  const isAudio = useMemo(
    () =>
      showAttachmentNameOnly
        ? false
        : ['audio/mpeg', 'audio/wav'].includes(attachment.mimeType),
    [attachment.mimeType, showAttachmentNameOnly],
  );

  const icon = useMemo(
    () =>
      hideIcons || isAudio ? null : (
        <span className="attached-preview mr-5">
          <div onClick={handleOnClick}>
            <Icon
              className={styles.itemColor}
              name={getFileIcon(attachment.mimeType)}
            />
          </div>
        </span>
      ),
    [hideIcons, handleOnClick, attachment, isAudio],
  );

  const handleDelete = useCallback(() => {
    onRemove && onRemove(attachment);
  }, [attachment, onRemove]);

  const handleLeave = useCallback(event => {
    event.preventDefault();
    setIsHover(false);
  }, []);

  const handleEnter = useCallback(event => {
    event.preventDefault();
    setIsHover(true);
  }, []);

  const handleMouseOver = useCallback(event => {
    event.preventDefault();
    setIsPreview(true);
  }, []);

  const handlePreviewClose = useCallback(() => {
    setIsPreview(false);
  }, []);

  const handleMouseOut = useCallback(
    event => {
      event.preventDefault();
      handlePreviewClose();
    },
    [handlePreviewClose],
  );

  const content = useMemo(() => {
    if (showAttachmentNameOnly) {
      return attachment.fileName;
    }
    switch (attachment.mimeType) {
      case 'audio/mpeg':
      case 'audio/wav': {
        return (
          <audio
            controls
            preload="metadata"
            src={attachment.url || attachment.preview}
          />
        );
      }

      default: {
        return attachment.fileName;
      }
    }
  }, [attachment, showAttachmentNameOnly]);

  const PreviewComponent = useMemo(
    () => attachmentPreviewComponent || AttachmentPreview,
    [attachmentPreviewComponent],
  );

  return (
    <li
      className={classnames({
        'bg-btn border-radius p-10': isThreadAttachment && !isAudio,
        [styles.wrapper]: !isThreadAttachment,
        [styles.wrapperWithoutPadding]: isThreadAttachment && !isAudio,
      })}
      onMouseEnter={handleEnter}
      onMouseLeave={handleLeave}
    >
      <div className="attached-content">
        <div
          className={classnames(styles.itemPosition, {
            [styles.itemPositionStrike]: hasLowStrike,
          })}
        >
          {attachment.uploadToken || !isDownloadable ? (
            <a
              className={classnames(styles.itemColor, 'file-name', {
                'no-margin-right': isThreadAttachment && !isAudio,
                'text-semibold': isThreadAttachment && !isAudio,
              })}
              id={elId}
            >
              {icon}
              {content}
            </a>
          ) : (
            <a
              className={classnames('file-name', styles.itemColor, {
                'no-margin-right': isThreadAttachment && !isAudio,
                'text-semibold': isThreadAttachment && !isAudio,
              })}
              href={attachment.url}
              id={elId}
              rel="noopener noreferrer"
              title={!attachmentPreviewComponent ? attachment.description : ''}
              onMouseOut={handleMouseOut}
              onMouseOver={handleMouseOver}
            >
              {icon}
              {content}
            </a>
          )}

          {isPreview && (
            <PreviewComponent
              anchorId={elId}
              attachment={attachment}
              isPreview={isPreview}
              onClose={handlePreviewClose}
            />
          )}
        </div>
        {editable && (isThreadAttachment || isHover) && (
          <a
            className={classnames(
              'close pt-5 pl-5 text-size-xlarge',
              styles.pt3,
            )}
            onClick={handleDelete}
          >
            ×
          </a>
        )}
      </div>
    </li>
  );
};

export default AttachmentItem;

interface IAttachmentItemProps {
  editable: boolean;
  attachment: IFileAttachmentTemp;

  isDownloadable?: boolean;
  hideIcons?: boolean;
  hasLowStrike?: boolean;
  onRemove?: Function;
  attachmentPreviewComponent?: Function;
  isThreadAttachment?: boolean;
  showAttachmentNameOnly?: boolean;
}

const getFileIcon = extension => {
  if (extension) {
    if (extension.includes('image')) {
      return 'file-picture';
    } else if (extension.includes('pdf')) {
      return 'file-pdf';
    } else if (extension.includes('audio')) {
      return 'file-music';
    } else if (extension.includes('video')) {
      return 'file-play';
    } else if (extension.includes('sheet')) {
      return 'file-excel';
    } else if (extension.includes('presentation')) {
      return 'file-presentation';
    } else if (extension.includes('document') || extension.includes('msword')) {
      return 'file-word';
    } else if (extension.includes('zip')) {
      return 'file-zip';
    } else if (extension.includes('text/plain')) {
      return 'file-text';
    } else if (extension.includes('application/octet-stream')) {
      return 'file-zip2';
    }
  }
  return 'file-empty';
};
