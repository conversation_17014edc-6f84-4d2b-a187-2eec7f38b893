.wrapper {
  font-weight: bold;
  overflow-y: hidden;
  padding: 4px 5px 3px 1px !important;
}

.wrapperWithoutPadding {
  font-weight: bold;
  overflow-y: hidden;
}

.itemColor {
  color: #333;
  font-size: 14px;
}

.itemPosition {
  margin-left: -5px !important;
  text-decoration: none;
  position: relative;
}

.itemPositionStrike:after {
  content: '';
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 1px;
  border-width: 0 0 1px;
  border-style: solid;
  border-color: #333 !important;
}

.nameCell {
  display: flex;
  align-items: center;
}

.pt3 {
  padding-top: 3px !important;
}