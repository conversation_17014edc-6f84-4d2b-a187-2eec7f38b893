import { useCallback } from 'react';
import { useApolloClient } from 'react-apollo';
import dataIdFromObject from '../../../../../app/dataIdFromObject';
import IMessage from '../../../../abstract/Messages/IMessage';
import IMessageItem from '../../../../abstract/Messages/IMessageItem';
import useCurrentMessage from './useCurrentMessage';

export default function useSetLastMessage(): IUseSetLastMessageRes {
  const client = useApolloClient();

  const { selectedItem } = useCurrentMessage();

  const setLastMessage = useCallback<(messageItem: IMessageItem) => void>(
    lastMessageItem => {
      if (
        selectedItem &&
        lastMessageItem.id !== selectedItem?.lastMessageItem?.id
      ) {
        client.writeData<IMessage>({
          id: dataIdFromObject(selectedItem) || undefined,
          data: {
            ...selectedItem,
            lastMessageItem,
          },
        });
      }
    },
    [selectedItem, client],
  );

  return {
    setLastMessage,
  };
}

interface IUseSetLastMessageRes {
  setLastMessage: (messageItem: IMessageItem) => void;
}
