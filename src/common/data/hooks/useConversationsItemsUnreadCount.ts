import { useQuery } from 'react-apollo';
import useSocket<PERSON>andler from '../socket/useSocketHandler';
import conversationsItemsUnreadCountQuery from '../../../modules/EdCom/Conversations/data/conversationsItemsUnreadCount.graphql';

export default function useConversationsItemsUnreadCount(): {
  conversationsItemsUnreadCount: number | undefined;
  refetch: () => void;
} {
  const { data: { conversationsItemsUnreadCount } = {}, refetch } = useQuery<{
    conversationsItemsUnreadCount: number;
  }>(conversationsItemsUnreadCountQuery);

  useSocketHandler('conversation', refetch);

  return {
    conversationsItemsUnreadCount,
    refetch,
  };
}
