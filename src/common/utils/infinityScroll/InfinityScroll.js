import classnames from 'classnames';
import PropTypes from 'prop-types';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import _ from 'lodash';

import Scroll from '../../components/other/Scroll';
import Empty from '../../components/utils/Empty';
import Spinner from '../../components/utils/Spinner';
import styles from './infinityScroll.scss';

const MIN_HEIGHT_TO_TRIGGER = 20;

const InfinityScroll = ({
  isLoadMoreLoading,
  onLoadMore,
  items,
  loading,
  renderItems,
  flatMapItems,
  height,
  errorMessage,
  className,
  hasMore,
  mode,
  hasEmptyPlaceholder = true,
  debounceTimeout = 0,
  showEmptyMessage = true,
}) => {
  const [scrollContainer, setScrollContainer] = useState(null);

  const [isInitialScrolled, setInitialScrolled] = useState(false);

  useEffect(() => {
    if (!isInitialScrolled && mode === 'DESC' && !_.isEmpty(items)) {
      scrollContainer.scrollTop = scrollContainer.scrollHeight;
      setInitialScrolled(true);
    }
  }, [scrollContainer, isInitialScrolled, items, mode]);

  const onContainerScroll = useCallback(
    _.debounce(() => {
      const ascCheck =
        mode === 'ASC' &&
        scrollContainer.scrollTop + scrollContainer.clientHeight >=
          scrollContainer.scrollHeight - MIN_HEIGHT_TO_TRIGGER;
      const descCheck =
        mode === 'DESC' && scrollContainer.scrollTop < MIN_HEIGHT_TO_TRIGGER;

      if (
        !isLoadMoreLoading &&
        onLoadMore &&
        hasMore &&
        (ascCheck || descCheck)
      ) {
        const currentScrollHeight = scrollContainer.scrollHeight;
        const resultPromise = onLoadMore();

        if (descCheck) {
          resultPromise.then(() => {
            const offset = 10;
            scrollContainer.scrollTop =
              scrollContainer.scrollHeight - currentScrollHeight - offset;
          });
        }
      }
    }, debounceTimeout),
    [isLoadMoreLoading, onLoadMore, hasMore, scrollContainer, mode],
  );

  useEffect(() => {
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', onContainerScroll);

      return () => {
        scrollContainer.removeEventListener('scroll', onContainerScroll);
      };
    }
  }, [onContainerScroll, scrollContainer]);

  const renderSpinner = useCallback(
    () => (
      <div className={styles.loading}>
        <Spinner />
      </div>
    ),
    [],
  );

  const renderEmpty = useCallback(
    () => (
      <div className={styles.empty}>
        <Empty />
      </div>
    ),
    [],
  );

  const flatItems = useMemo(() => [].concat(...items.map(flatMapItems)), [
    flatMapItems,
    items,
  ]);

  const isEmpty = useMemo(
    () => !loading && !flatItems.length && hasEmptyPlaceholder,
    [flatItems.length, loading, hasEmptyPlaceholder],
  );

  return (
    <Scroll
      autoHeightMax={height}
      className={classnames(styles.scroll, className)}
      getRef={setScrollContainer}
    >
      {errorMessage}
      {showEmptyMessage && isEmpty && !isLoadMoreLoading ? renderEmpty() : null}
      {loading ? renderSpinner() : null}
      {isLoadMoreLoading && mode === 'DESC' ? renderSpinner() : null}
      {!isEmpty ? renderItems(flatItems) : null}
      {isLoadMoreLoading && mode === 'ASC' ? renderSpinner() : null}
    </Scroll>
  );
};

InfinityScroll.propTypes = {
  className: PropTypes.string,
  loading: PropTypes.bool,
  items: PropTypes.arrayOf(PropTypes.any),
  renderItems: PropTypes.func,
  errorMessage: PropTypes.string,
  flatMapItems: PropTypes.func,
  onLoadMore: PropTypes.func,
  isLoadMoreLoading: PropTypes.bool,
  hasMore: PropTypes.bool,
  height: PropTypes.number,
  debounceTimeout: PropTypes.number,
  showEmptyMessage: PropTypes.bool,
};

InfinityScroll.defaultProps = {
  className: null,
  loading: false,
  items: [],
  renderItems: items =>
    // eslint-disable-next-line react/no-array-index-key
    items.map((item, idx) => <span key={idx}>{item.name}</span>),
  errorMessage: null,
  flatMapItems: item => [item],
  onLoadMore: null,
  isLoadMoreLoading: false,
  height: 300,
  hasMore: true,
  debounceTimeout: 0,
  showEmptyMessage: true,
};

export default InfinityScroll;
