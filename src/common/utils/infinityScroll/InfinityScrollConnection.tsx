import React from 'react';

import { DocumentNode } from 'graphql';

import InfinityScroll from './InfinityScroll';
import useConnection from './hooks/useConnection';
import TFetchMode from './FetchMoreScroll/abstract/TFetchMode';

interface IInfinityScrollConnection<T> {
  gqlConnectionQuery: DocumentNode;
  gqlConnectionVariables?: {};
  className?: string;
  height?: number;
  flatMapItems?: Function;
  renderItems?: (items: T[]) => JSX.Element[] | JSX.Element;
  mode?: TFetchMode;
  itemsPerFetch?: number;
  isSkip?: boolean;
  items?: T[];
  onNewDataLoaded?: (items: T[]) => void;
  hasEmptyPlaceholder?: boolean;
  anchor?: number | string;
  showEmptyMessage?: boolean;
}

function InfinityScrollConnection<T extends {}>({
  gqlConnectionQuery,
  gqlConnectionVariables,
  className,
  height,
  flatMapItems,
  renderItems,
  mode,
  itemsPerFetch,
  isSkip,
  items,
  onNewDataLoaded,
  hasEmptyPlaceholder = false,
  showEmptyMessage = true,
  anchor,
}: IInfinityScrollConnection<T>) {
  const {
    errorMessage,
    items: _items,
    hasMore,
    isLoadMoreLoading,
    isLoading,
    onLoadMore,
  } = useConnection<T>({
    gqlConnectionQuery,
    gqlConnectionVariables,
    mode,
    isSkip,
    itemsPerFetch,
    onNewDataLoaded,
    anchor,
  });

  return (
    <InfinityScroll
      className={className}
      errorMessage={errorMessage}
      flatMapItems={flatMapItems}
      hasEmptyPlaceholder={hasEmptyPlaceholder}
      hasMore={hasMore}
      height={height}
      isLoadMoreLoading={isLoadMoreLoading}
      items={items || _items}
      loading={isLoading}
      mode={mode}
      renderItems={renderItems}
      showEmptyMessage={showEmptyMessage}
      onLoadMore={onLoadMore}
    />
  );
}

export default InfinityScrollConnection;
