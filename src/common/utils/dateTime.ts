import {
  find,
  isDate,
  isEmpty,
  isNaN,
  isString,
  List,
  toInteger,
  toNumber,
} from 'lodash';
import moment, { Moment, MomentInput } from 'moment';

export const MINUTES_PER_HOUR = 60;
export const MINUTES_PER_DAY = 1440;
export const MILLISECONDS_PER_SECONDS = 1000;
export const MILLISECONDS_PER_MINUTE = 60000;
export const MINUTES_DIGITS = 2;
export const HOURS_FOR_SHORT_FORMAT = 12;

export const ONE_HOUR = 3600000;
export const TWO_HOURS = 7200000;
export const ONE_DAY = 86400000;
export const TWO_DAYS = 172800000;
export const ONE_YEAR = 31536000000;

export const WEEK_DAYS = 7;
export const MONTH_WEEKS = 4;
export const MAX_YEARS_LIMIT = 10;

export const ViewDateFormat = 'MMM D YYYY';
export const ViewDateTimeFormat = 'MMM D YYYY, h:mm:ssa';

export const MomentDateFormat = 'D MMM YYYY';
export const MomentDateTimeFormat = 'D MMM YYYY HH:mm';
export const MomentDateTimeFriendlyFormat = 'DD MMM YYYY HH:mm';

export const TransportFormat = 'YYYY-MM-DD';
export const TransportDateTimeFormat = 'YYYY-MM-DDTHH:mm:ssZ';
export const CalendarTimeFormat = 'YYYY-MM-DDTHH:mm:ss';

export const DatePickerDateFormat = 'd M yyyy';

export const ShortDateFormat = 'MMM DD';

export const TimeFormat = 'HH:mm';
export const TimeFormatSeconds = 'HH:mm:ss';
export const ShortTimeFormatSeconds = 'mm:ss';
export const ExtendedTimeFormat = 'hh:mma';

export const UpdatedAt = 'ddd DD MMM YYYY, h:mm A';
export const Edana4 = 'ddd, DD MMM YYYY, hh:mm a';

export const ValidatedAt = 'ddd, DD MMM YYYY, h:mm a';
export const Edana7 = 'D MMM YYYY, h:mm a';
export const Edana10 = 'ddd, D MMM YYYY, hh:mm a';

export const timetableDateFormat = 'DD MMM YYYY';

export const notifFormat = 'MMM YYYY';
export const CreatedAt = 'ddd, DD MMM YYYY, h:mm A';

export const ShortWeekDateFormat = 'ddd DD MMM';

export const DateWithWeekFormat = 'ddd DD MMM YYYY';
export const startDateFormat = 'ddd, DD MMM YYYY';
export const weekdayShotFormat = 'ddd';

export const TODAY_INDEX = 0;
export const YESTERDAY_INDEX = 1;

export const format = (date: MomentInput, f = ViewDateFormat) =>
  moment(date).format(f);

export const humanizeDuration = (value: MomentInput): string =>
  moment.duration(moment(value).diff(moment()), 'milliseconds').humanize(true);

export const formatDateTimeLocal = (
  date: MomentInput,
  language = 'en',
  f = MomentDateTimeFriendlyFormat,
) => moment(date, 'DD MMM[.] YYYY HH:mm', language).format(f);

type TDateConstructorParam = string | number | Date;
type TLatestComparatorKey = string | number;
type TLatestComparatorObject =
  | Record<TLatestComparatorKey, TDateConstructorParam>
  | TDateConstructorParam[];

export const latestComparator = (key: TLatestComparatorKey) => (
  a: TLatestComparatorObject,
  b: TLatestComparatorObject,
) => new Date(b[key]).valueOf() - new Date(a[key]).valueOf();

const YEAR_PART_TO_SHOW = 2;

interface FormatYearsParams {
  startDate: MomentInput;
  endDate: MomentInput;
}

export const formatYears = ({ startDate, endDate }: FormatYearsParams) => {
  const startYear = moment(startDate).format('YYYY');
  const endYear = moment(endDate).format('YYYY');

  return startYear === endYear
    ? startYear
    : `${startYear}/${endYear.substr(endYear.length - YEAR_PART_TO_SHOW)}`;
};

export const extractYear = (dateString: string): number =>
  moment(dateString).year();

interface IDateOverlappingRange {
  id: number;
  startDate: MomentInput;
  endDate: MomentInput;
}

interface IDateOverlappingParams {
  ranges: List<IDateOverlappingRange>;
  fromCheck: MomentInput;
  toCheck: MomentInput;
  customConditionItem: number;
}

export const dateOverlapping = ({
  ranges,
  fromCheck,
  toCheck,
  customConditionItem,
}: IDateOverlappingParams) =>
  find(
    ranges,
    ({ id, startDate, endDate }) =>
      (moment(fromCheck).isBetween(moment(startDate), moment(endDate)) ||
        moment(toCheck).isBetween(moment(startDate), moment(endDate)) ||
        moment(startDate).isBetween(moment(fromCheck), moment(toCheck)) ||
        moment(endDate).isBetween(moment(fromCheck), moment(toCheck))) &&
      id !== customConditionItem,
  );

export const isMinutesOverlapped = (
  [from, to]: [number, number],
  [start, end]: [number, number],
): boolean =>
  (start <= from && from < end) ||
  (start < to && to <= end) ||
  (from <= start && start < to) ||
  (from < end && end <= to);

export function fromMinutesToTime(minutes: number) {
  return moment
    .utc(new Date(minutes * MILLISECONDS_PER_MINUTE))
    .format(TimeFormat);
}

export function fromStringToMinutes(time: string) {
  const value = moment.utc(`1970-01-01 ${time}`).toDate().valueOf();

  return Math.floor(value / MILLISECONDS_PER_MINUTE);
}

export function daysDiff(date1: MomentInput, date2: MomentInput) {
  return moment(date1).diff(date2, 'days');
}
export function isToday(date: MomentInput) {
  return moment().isSame(moment(date), 'day');
}

export function toShortWeekday(date: MomentInput) {
  return moment(date).format(weekdayShotFormat);
}

export function durationAsHours(value: number, format: '12' | '24' = '12') {
  const minutes = value % MINUTES_PER_HOUR;
  let hours = (value - minutes) / MINUTES_PER_HOUR;
  const minutesZeroLead = `0${minutes}`;

  const is12 = format === '12';

  let postFix = hours / HOURS_FOR_SHORT_FORMAT < 1 ? ' am' : ' pm';

  postFix = is12 ? postFix : '';

  hours =
    is12 && hours !== HOURS_FOR_SHORT_FORMAT
      ? hours % HOURS_FOR_SHORT_FORMAT
      : hours;

  return `${hours}:${minutesZeroLead.substr(-MINUTES_DIGITS)}${postFix}`;
}

export function toHoursAndMinutes(value: number) {
  if (!value) {
    return '00:00';
  }

  const minutes = value % MINUTES_PER_HOUR;
  const hours = (value - minutes) / MINUTES_PER_HOUR;
  const minutesZeroLead = `0${minutes}`;

  return `${hours || '00'}:${minutesZeroLead.substr(-MINUTES_DIGITS)}`;
}

export function iterateDays(
  from: Moment,
  to: Moment,
  check: (value: Moment) => boolean,
) {
  const _from: Moment = moment(from);
  const days: Moment[] = [];

  while (_from.isSameOrBefore(to)) {
    if (check(_from)) {
      days.push(moment(_from));
    }

    _from.add(1, 'days');
  }

  return days;
}

export const getAge = (
  birthDay: string,
): { years: number; months: number; days: number } => {
  const age = moment.duration(moment().diff(moment(birthDay)));

  return { years: age.years(), months: age.months(), days: age.days() };
};

export const getFormattedAge = (
  birthDay: string,
  t,
  { isIncludeMonth = false }: { isIncludeMonth?: boolean },
): string => {
  const age = getAge(birthDay);
  return `${age.years}${t('y')}${
    isIncludeMonth && age.months ? ` ${age.months}${t('m')}` : ''
  }`;
};

export function unixStringToNumber(timestamp: TDateConstructorParam) {
  if (
    isString(timestamp) &&
    !isDate(timestamp) &&
    !isNaN(toNumber(timestamp))
  ) {
    return toNumber(timestamp);
  }
  return timestamp;
}

export function getReadableDayString(
  time: TDateConstructorParam,
  t: (p: string, o?: object) => string,
): string {
  const days = moment().diff(time, 'days');
  if (days < WEEK_DAYS) {
    switch (days) {
      case TODAY_INDEX:
        return t('Today');
      case YESTERDAY_INDEX:
        return t('Yesterday');
      default:
        return `${days} ${t('days ago')}`;
    }
  }

  return '';
}

export function getReadableWeekString(
  time: TDateConstructorParam,
  t: (p: string, o?: object) => string,
): string {
  const weeks = moment().diff(time, 'weeks');
  if (weeks <= MONTH_WEEKS) {
    return `${weeks} ${t(`#{week} ago`, {
      week: weeks > 1 ? 'weeks' : 'week',
    })}`;
  }

  return '';
}

export function getReadableString(
  time: TDateConstructorParam,
  t: (p: string, o?: object) => string,
  locale,
): string {
  moment.locale(locale);
  const parsedTime = unixStringToNumber(time);
  const days = getReadableDayString(parsedTime, t);
  const weeks = getReadableWeekString(parsedTime, t);

  switch (true) {
    case !isEmpty(days):
      return days;
    case !isEmpty(weeks):
      return weeks;
    default:
      return format(parsedTime, notifFormat);
  }
}

type TDurationFormat = 'days' | 'weeks' | 'months' | 'years';

export function getDurationString(
  durationFormat: TDurationFormat,
  startDate: string,
  endDate: string,
  t?: (p: string, o?: object) => string,
) {
  let message;

  const startMomentDate = moment(startDate, TransportFormat);
  const endMomentDate = moment(endDate, TransportFormat);

  switch (durationFormat) {
    case 'days':
      const days = endMomentDate.diff(startMomentDate, 'days') + 1;
      message = days && `${days} ${t ? t('days') : 'days'}`;
      break;
    case 'months':
      const months = endMomentDate.diff(startMomentDate, 'months') + 1;

      message = months && `${months} ${t ? t('months') : 'months'}`;
      break;
    case 'years':
      const years = endMomentDate.diff(startMomentDate, 'years') + 1;
      message = years && `${years} ${t ? t('years') : 'years'}`;
      break;
    default:
      throw new Error('Wrong DateRangePicker duration format');
  }

  return message;
}

export const milliSecondsToSec = (ms: number): number =>
  toInteger(ms / MILLISECONDS_PER_SECONDS);

export const milliSecondsToMin = (ms: number): number =>
  toInteger(ms / MILLISECONDS_PER_MINUTE);

export const milliSecondsToHours = (ms: number): number =>
  toInteger(ms / ONE_HOUR);

export const milliSecondsToDays = (ms: number): number =>
  toInteger(ms / ONE_DAY);
