import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import {
  filter,
  get,
  includes,
  isEmpty,
  map,
  findIndex,
  keyBy,
  groupBy,
  mapValues,
  find,
} from 'lodash';
import {
  Route,
  useHistory,
  useLocation,
  useRouteMatch,
} from 'react-router-dom';
import { useMutation, useQuery } from 'react-apollo';
import classNames from 'classnames';
import { ApolloQueryResult } from 'apollo-client';

import GqlFullCrud from '../../../../../../../common/components/dataViews/GqlFullCrud';
import eContentItemContent from '../../../../../../../common/data/eContent/eContentItemContent.graphql';
import createEContentItemContent from '../../../../../../../common/data/eContent/createEContentItemContent.graphql';
import updateEContentItemContent from '../../../../../../../common/data/eContent/updateEContentItemContent.graphql';
import moveEContentItemContentGql from '../../../../../../../common/data/eContent/moveEContentItemContent.graphql';
import eContentItemContents from '../../../../../../../common/data/eContent/eContentItemContents.graphql';
import eContentItemContentsCount from '../../../../../../../common/data/eContent/eContentItemContentsCount.graphql';
import cookColumns from './tableColumns';

import ContentViewTypes, {
  LIST,
  LIST_DETAIL,
  TViewType,
} from '../../../../../../../model/ContentViewTypes';
import ContentSortingTypes, {
  TSortingType,
} from '../../../../../../../model/ContentSortingTypes';

import StatusWithDraft, {
  Active,
} from '../../../../../../../model/StatusWithDraft';
import EContentContentCard from './EContentContentCard';
import useT from '../../../../../../../common/components/utils/Translations/useT';
import FilterBar from '../../../../../../../common/components/controls/FilterBar';
import { IEContentItemForm } from '../../EContentItemForm';
import EditSessionRender from '../../../../../../../common/components/containers/EditSessionProvider/EditSessionRender';
import PanelButton from '../../../../../../../common/components/controls/PanelButtons/PanelButton';
import EContentItemContentForm from './form';
import styles from './EContentItemContentsTab.scss';
import SubmitSection from '../EContentItemDetailsTab/SubmitSection';
import { IDropdownAction } from '../../../../../../../common/components/controls/Dropdown';
import IEContentContent from '../../../../../../../common/abstract/EContent/IEContentContent';
import Modal from '../../../../../../../common/components/utils/Modal';
import ContentMoveModal from './ContentMoveModal';
import { cookEContentClobs } from './form/EContentItemContentForm';
import LoadingMask from '../../../../../../../common/components/utils/LoadingMask';
import { useRefetchSingleQuery } from '../../../../../../../common/components/utils/RefetchQueries/useRefetchSingleQuery';
import eContentResourceXLanguages from '../../../../../../../common/data/eContent/eContentResourceXLanguages.graphql';
import getGqlOperationName from '../../../../../../../common/utils/getGqlOperationName';
import { ADD_CONTENT_REGEX } from '../../../../../../../common/utils/regexp';
import GqlFullCrudTable from '../../../../../../../common/components/dataViews/GqlFullCrudTable';
import Notifications from '../../../../../../../common/utils/Notifications';
import moveEContentSequence from '../../../../../../../common/data/eContent/moveEContentSequence.graphql';
import EditSessionContext from '../../../../../../../common/components/containers/EditSessionProvider/EditSessionContext';
import IEContentResource from '../../../../../../../common/abstract/EContent/IEContentResource';
import useFilterStore from '../../../../../../../common/components/controls/FilterBar/hooks/useFilterStore';
import ContentViewSelector from '../../../../../../../common/components/controls/FilterBar/ContentViewSelector';
import { redirectNewTab } from '../../../../../../../common/utils/urlHelper';
import swal2 from '../../../../../../../common/utils/swal2';
import {
  compareObjects,
  resourceAttributeChecker,
} from './ResourceAttributeChecker';
import {
  BasicById,
  textType,
} from '../../../../../../../model/EContentResourceXAttributeTypes';
import ContentsMoveModal from './ContentsMoveModal';
import {
  Simple,
  Rich,
  SimpleAndRich,
} from '../../../../../../../model/TextsType';
import { RichType, SimpleType } from '../../../../../../../model/ClobsType';

const DefaultGridPageSize = 12;
const TWO = 2;
const THOUSAND = 1000;
const TEN = 10;
const EContentItemContentsTab: React.FC<IEContentItemForm> = ({
  entity,
  libraryName,
  goToCreateItem,
  usedKeywords,
  onGoBack,
  onDelete,
  refetchTranslationSupport,
  floatButton = false,
}) => {
  const t = useT();
  const history = useHistory();
  const { goBack } = useHistory();
  type stateProp = {
    fromPage?: string;
    pathname?: string;
    state?: { fromPage?: string };
  };
  const location: stateProp = useLocation() as stateProp;
  const { isEditSessionActive } = useContext(EditSessionContext);
  const _onGoBack = useCallback(() => {
    const { fromPage, state } = location;
    if (
      fromPage === 'contentview' ||
      (state && state.fromPage && state.fromPage === 'contentview')
    ) {
      goBack();
    } else {
      onGoBack();
    }
  }, [location, onGoBack, goBack]);
  const linkNewTab = useMemo(() => {
    const { pathname } = location;
    return !!pathname && ADD_CONTENT_REGEX.test(pathname);
  }, [location]);

  const refetchSingleQuery = useRefetchSingleQuery();

  const [contentToMove, setContentToMove] = useState<IEContentContent | null>(
    null,
  );
  const [itemsList, setItemsList] = useState<IEContentContent[]>([]);
  const [contentView, setContentView] = useState<TViewType>('LOADING');
  const [contentSorting, setContentSorting] = useState<TSortingType>(
    ContentSortingTypes.RECENTALY_UPDATED.value,
  );

  const { url } = useRouteMatch();
  const resource = useMemo(() => get(entity, 'resource'), [entity]);
  const itemId = useMemo(() => get(entity, 'id'), [entity]);
  const libraryId = useMemo(() => get(resource, 'libraryId'), [resource]);
  const filterKey = useMemo(() => `E_CONTENT_ITEMS_CONTENT_LIST_${libraryId}`, [
    libraryId,
  ]);
  const columns = useMemo(() => cookColumns(t, contentView, resource), [
    t,
    contentView,
    resource,
  ]);

  const availableColumns = useMemo(
    () => [
      columns[3],
      columns[4],
      columns[5],
      columns[6],
      columns[7],
      columns[8],
      columns[9],
      columns[10],
    ],
    [],
  );
  const deafultSelectedColumns = useMemo(
    () => [columns[3], columns[4], columns[5]],
    [],
  );
  const [selectedColumns, setSelectedColumns] = useState<Array<string>>(
    map(deafultSelectedColumns as Array<ITableColumn>, 'id'),
  );
  const filteredColumns = useMemo(
    () =>
      contentView === LIST.value
        ? columns
        : filter(
            columns,
            ({ id, type }) =>
              includes(selectedColumns, id) ||
              type === 'always-display' ||
              type === 'options',
          ),
    [selectedColumns, contentView, columns],
  );

  const queryRes = useQuery(eContentResourceXLanguages, {
    variables: { resourceId: resource.id, itemId: entity.id },
  });
  const languageIds = useMemo(() => {
    const languageList = get(
      queryRes,
      `data.${getGqlOperationName(eContentResourceXLanguages)}`,
      [],
    );
    return map(languageList, item => item.languageId);
  }, [eContentResourceXLanguages, queryRes]);

  const {
    loading: fLoading,
    values: defaultFilterValues = {
      status: [Active.value],
      languageId: resource?.languageIds || languageIds || [],
      sortOrder: contentSorting,
      customizeColumn: selectedColumns,
      itemId: entity.id,
      view: contentView,
      count: DefaultGridPageSize,
      first: 0,
      searchQuery: '',
    },
    onChange: onDefaultFilterValuesChange,
  } = useFilterStore(filterKey, {
    defaultValues: {
      status: [Active.value],
      languageId: languageIds || [],
      sortOrder: contentSorting,
      customizeColumn: selectedColumns,
      itemId: entity.id,
      view: contentView,
      count: DefaultGridPageSize,
      first: 0,
      searchQuery: '',
    },
  });
  // useEffect(() =>
  //   // setFiltersValue(preState => ({ ...preState, languageId: languageIds })),
  //   [languageIds],
  // );
  useEffect(() => {
    setContentView(
      !defaultFilterValues.view
        ? ContentViewTypes.GRID.value
        : defaultFilterValues.view,
    );
    setContentViewInitial(
      !defaultFilterValues.view
        ? ContentViewTypes.GRID.value
        : defaultFilterValues.view,
    );
    setContentSorting(defaultFilterValues.sortOrder);
    defaultFilterValues.customizeColumn &&
      setSelectedColumns(defaultFilterValues.customizeColumn);
  }, [defaultFilterValues]);

  const [movedItems, setMovedItems] = useState<number>(0);
  const updateMovedItems = useCallback(() => {
    setMovedItems(prev => (prev += 1));
  }, []);
  const variables = useMemo(
    () => ({
      itemId: entity.id,
      // eslint-disable-next-line no-magic-numbers
      id: Number(location.pathname?.split('/')[9]) || undefined,
      // eslint-disable-next-line no-magic-numbers
      key: location.pathname?.split('/')[9] || null,
    }),
    [entity, location],
  );

  const cookQueryVariables = useCallback(
    filters => ({
      ...filters,
      itemId: entity.id,
      moved: movedItems,
    }),
    [entity, movedItems],
  );

  const leftLabel = useCallback(
    ({ status }) => t(StatusWithDraft.BasicByValue[status]?.name),
    [t],
  );
  const middleLabel = useCallback(
    content => <span className="ml-5">{t(content.language?.name)}</span>,
    [t],
  );

  const filterLanguages = useCallback(
    options =>
      isEmpty(languageIds)
        ? options
        : filter(options, option => includes(languageIds, option?.id)),
    [languageIds],
  );

  const goToCreate = useCallback(() => history.push(`${url}/add/details`), [
    history,
    url,
  ]);

  const goToContents = useCallback(() => history.push(url), [history, url]);

  const [showMoveContentsModal, setShowMoveContentsModal] = useState<boolean>(
    false,
  );

  const openMoveContentsModal = useCallback(() => {
    setShowMoveContentsModal(true);
  }, []);

  const closeMoveContentsModal = useCallback(() => {
    setShowMoveContentsModal(false);
  }, []);

  const rightActionButtons = useMemo(
    () => (
      <EditSessionRender>
        <>
          <PanelButton
            icon="arrow-right16"
            title={t('Move Contents')}
            onClick={openMoveContentsModal}
          />
          <PanelButton
            icon="plus3"
            title={t('Add Content')}
            onClick={goToCreate}
          />
        </>
      </EditSessionRender>
    ),
    [t, goToCreate, openMoveContentsModal],
  );

  const cardTitle = useCallback(
    content => {
      const cookedClobs = cookEContentClobs(
        content.clobs,
        resource?.attributes,
      );
      return cookedClobs?.heading?.content;
    },
    [resource],
  );

  const cardBody = useCallback(
    props =>
      contentView === ContentViewTypes.GRID_TIMESTAMP.value ? (
        <EContentContentCard {...props} resource={resource} />
      ) : (
        <></>
      ),
    [resource, contentView],
  );

  const addExtraActions = useCallback<
    (isEditing: boolean, node: IEContentContent) => IDropdownAction[]
  >(
    (isEditing, node) => {
      const actions: IDropdownAction[] = [];

      if (isEditing) {
        actions.push({
          sequence: 2,
          label: t('Move'),
          icon: 'arrow-right16',
          onClick: () => setContentToMove(node),
        });
      }

      return actions;
    },
    [t, setContentToMove],
  );
  const additionalOptionsList = useCallback<
    (node: IEContentContent) => IDropdownAction[]
  >(
    node =>
      !!isEditSessionActive
        ? [
            {
              sequence: 2,
              label: t('Move'),
              icon: 'arrow-right16',
              onClick: () => setContentToMove(node),
            },
          ]
        : [],
    [t, isEditSessionActive],
  );
  const [_moveEContentSequence, { loading: isSequenceMoving }] = useMutation(
    moveEContentSequence,
  );

  function hasDecimalValue(str) {
    const num: any = parseFloat(str); // Convert to a number
    return num % 1 !== 0 && num.toFixed(TWO) % 1 !== 0; // Check if it's a decimal with non-zero fractional part
  }

  const onMoveSequence = useCallback(
    async (_sequenceFrom: number, _sequenceTo: number) => {
      const sequenceFrom = itemsList[_sequenceFrom].sequence;
      const sequenceTo = itemsList[_sequenceTo].sequence;

      await _moveEContentSequence({
        variables: {
          id: itemId,
          sequenceFrom: sequenceFrom.toFixed(TWO),
          sequenceTo: sequenceTo.toFixed(TWO),
        },
      });
      Notifications.success(t('Updated Succesfully'), '', t);
      return true;
    },
    [_moveEContentSequence, itemsList, t, itemId],
  );
  const onDragSortUpdate = useCallback(
    async (initialIndex, finalIndex) => {
      if (initialIndex === finalIndex) {
        return false;
      }

      const sequenceFrom = itemsList[initialIndex].sequence;
      const sequenceTo = itemsList[finalIndex].sequence;

      if (hasDecimalValue(sequenceFrom) || hasDecimalValue(sequenceTo)) {
        Notifications.error(
          'Error',
          `Cannot move item with decimal sequence.`,
          t,
        );
        return false;
      }

      await onMoveSequence(initialIndex, finalIndex);
      return true;
    },
    [onMoveSequence, itemsList, t],
  );

  const handleClose = useCallback(() => setContentToMove(null), []);

  const [moveEContentItemContent, { loading: isMoving }] = useMutation(
    moveEContentItemContentGql,
    {
      refetchQueries: [
        {
          query: eContentItemContents,
          variables: { ...defaultFilterValues, itemId: entity.id },
        },
        {
          query: eContentItemContentsCount,
          variables: { ...defaultFilterValues, itemId: entity.id },
        },
        ...refetchSingleQuery,
      ],
      awaitRefetchQueries: true,
    },
  );

  const maxes = [
    'maxNumberOfTextBoxes',
    'maxNumberOfDocuments',
    'maxNumberOfImages',
    'maxNumberOfVideoFiles',
    'maxNumberOfAudioFiles',
    'maxNumberOfUrls',
    'maxNumberOfQuestion',
  ];
  const attributeNames = {
    text: { name: 'Simple Text', maxKey: 'maxNumberOfTextBoxes' },
    richText: { name: 'Rich Text', maxKey: 'maxNumberOfTextBoxes' },
    document: { name: 'Documents', maxKey: 'maxNumberOfDocuments' },
    image: { name: 'Images', maxKey: 'maxNumberOfImages' },
    videoFile: { name: 'Video Files', maxKey: 'maxNumberOfVideoFiles' },
    audioFile: { name: 'Audio Files', maxKey: 'maxNumberOfAudioFiles' },
    url: { name: 'URLs', maxKey: 'maxNumberOfUrls' },
    questionTitle: { name: 'Questions', maxKey: 'maxNumberOfQuestion' },
    question: { name: 'Questions', maxKey: 'maxNumberOfQuestion' },
  };

  const handleMoveContent = useCallback(
    async item => {
      if (!contentToMove) {
        return;
      }
      const { clobs, language } = contentToMove;

      const eContentResourceXAttributes = resource?.attributes;
      const hash = keyBy(eContentResourceXAttributes, 'id');

      const sourceResourceTextValue = find(
        eContentResourceXAttributes,
        ({ attributeId }) => attributeId === textType.id,
      );

      const destinationResourceTextValue = find(
        item.resource?.attributes,
        ({ attributeId, isChecked, status }) =>
          attributeId === textType.id && !!isChecked && status === 'ACTIVE',
      );

      const hasSimpleText = clobs.find(
        x =>
          x.resourceAttributeId === sourceResourceTextValue?.id &&
          x.contentClobTypeId === SimpleType.value,
      );
      const hasRichText = clobs.find(
        x =>
          x.resourceAttributeId === sourceResourceTextValue?.id &&
          x.contentClobTypeId === RichType.value,
      );

      const attributeList = mapValues(
        groupBy(
          filter(
            map(clobs, (item, index) => ({
              id: hash[item.resourceAttributeId]?.attributeId,
              value: item.content,
              name:
                attributeNames[
                  BasicById[hash[item.resourceAttributeId]?.attributeId]?.name
                ]?.maxKey,
            })),
            ({ name }) => maxes.includes(name),
          ),
          'name',
        ),
        item => item.length,
      );

      const seletedItemMax = resourceAttributeChecker({
        resource: item.resource,
      });

      if (!includes(item.resource.languageIds, language?.id)) {
        await swal2({
          title: t(
            `Resource structure of the selected item does not support the #{name} Language.`,
            {
              name: language?.name,
            },
          ),
          icon: 'warning',
          className: classNames(styles.modalWrapper),
          confirmButtonText: t('Ok'),
        });
        return;
      }

      const data = compareObjects(attributeList, seletedItemMax);

      if (data) {
        await swal2({
          title: t(data),
          icon: 'warning',
          className: classNames(styles.modalWrapper),
          confirmButtonText: t('Ok'),
        });
        return;
      }

      if (hasSimpleText || hasRichText) {
        if (!destinationResourceTextValue) {
          await swal2({
            title: t(
              `Resource structure of the selected item does not support the Text.`,
            ),
            icon: 'warning',
            className: classNames(styles.modalWrapper),
            confirmButtonText: t('Ok'),
          });
          return;
        } else if (
          hasSimpleText &&
          hasRichText &&
          destinationResourceTextValue.value !== SimpleAndRich.value
        ) {
          await swal2({
            title: t(
              `Resource structure of the selected item does not support #{type} Text.`,
              {
                type:
                  destinationResourceTextValue.value === 1 ? 'Rich' : 'Simple',
              },
            ),
            icon: 'warning',
            className: classNames(styles.modalWrapper),
            confirmButtonText: t('Ok'),
          });
          return;
        } else if (
          hasSimpleText &&
          destinationResourceTextValue.value !== Simple.value &&
          destinationResourceTextValue.value !== SimpleAndRich.value
        ) {
          await swal2({
            title: t(
              `Resource structure of the selected item does not support Simple Text.`,
            ),
            icon: 'warning',
            className: classNames(styles.modalWrapper),
            confirmButtonText: t('Ok'),
          });
          return;
        } else if (
          hasRichText &&
          destinationResourceTextValue.value !== Rich.value &&
          destinationResourceTextValue.value !== SimpleAndRich.value
        ) {
          await swal2({
            title: t(
              `Resource structure of the selected item does not support Rich Text.`,
            ),
            icon: 'warning',
            className: classNames(styles.modalWrapper),
            confirmButtonText: t('Ok'),
          });
          return;
        }
      }

      const { id } = contentToMove;
      setContentToMove(null);

      try {
        await moveEContentItemContent({
          variables: {
            id,
            itemId: item.id,
            resourceId: item.resource.id,
          },
        });
        Notifications.success(t('Moved Succesfully'), '', t);
        updateMovedItems();
      } catch (error) {
        await swal2({
          text: t(`#{error}`, {
            error: (error as Error)?.message,
          }),
          icon: 'warning',
          className: classNames(styles.modalWrapper),
          confirmButtonText: t('Ok'),
        });
      }
    },
    [moveEContentItemContent, contentToMove, updateMovedItems],
  );

  const onFilterChange = useCallback(
    filters => {
      onDefaultFilterValuesChange(filters);
    },
    [onDefaultFilterValuesChange],
  );

  const modalTitle = useMemo(() => {
    if (!contentToMove || !resource) {
      return 'Move content to another item';
    }
    const cookedClobs = cookEContentClobs(
      contentToMove.clobs,
      resource?.attributes,
    );
    return `Move '${cookedClobs?.heading?.content}' to another item in ${libraryName}`;
  }, [contentToMove, resource]);

  const resolveFormTitle = useCallback(
    ({ clobs }) => {
      const cookedClobs = cookEContentClobs(clobs, resource?.attributes);
      return cookedClobs?.heading?.content || 'Content';
    },
    [resource, libraryName],
  );
  const handleCookItems = useCallback(items => {
    setItemsList(items);
    return items;
  }, []);

  const [contentViewInitial, setContentViewInitial] = useState('LOADING');
  const contentViewSelector = useCallback(props => {
    const _onChange = values => {
      props.onChange(values);
      setContentViewInitial(values);
    };

    return <ContentViewSelector {...props} onChange={_onChange} />;
  }, []);

  useEffect(() => {
    if (
      !isMoving &&
      !isSequenceMoving &&
      !fLoading &&
      contentView === 'LOADING'
    ) {
      setContentView(ContentViewTypes.GRID.value);
    }
  }, [contentView, isSequenceMoving, fLoading, isMoving]);

  const renderTitle = useCallback(title => <h5 title={title}>{title}</h5>, []);

  const handleRowClick = useCallback(
    item => {
      const foundIdx = findIndex(itemsList, { id: item.id });
      if (foundIdx.toString() === '-1') return;
      const contentIndex = foundIdx + defaultFilterValues.first;
      if (contentIndex.toString() === '-1') return;
      const calcParams = {
        contentIndex: contentIndex.toString(),
        contentFilters: JSON.stringify(defaultFilterValues),
      };
      if (defaultFilterValues.searchQuery)
        Object.assign(calcParams, {
          searchQuery: defaultFilterValues.searchQuery,
        });

      const params = `?${new URLSearchParams(calcParams).toString()}`;
      const contentUrl = `${url}/edit/${item.id}${params}`;
      redirectNewTab(contentUrl);
    },
    [url, defaultFilterValues, itemsList],
  );

  const handleRowClickGrid = useCallback(
    item => {
      const createdAt = new Date(item.createdAt);
      const currentDate = new Date();
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      const timeDifference = Math.abs(currentDate - createdAt);
      const timeDifferenceInSeconds = timeDifference / THOUSAND;
      const contentUrl = `${url}/edit/${item.id}`;
      if (timeDifferenceInSeconds >= TEN) {
        redirectNewTab(contentUrl);
      } else {
        history.push(contentUrl);
      }
    },
    [url, history],
  );

  const canDrag = useCallback(row => row.sequence % 1 === 0, []);

  return (
    <LoadingMask
      active={
        isMoving || isSequenceMoving || fLoading || contentView === 'LOADING'
      }
    >
      <span className={styles.crud}>
        {[
          ContentViewTypes.LIST.value,
          ContentViewTypes.LIST_DETAIL.value,
        ].includes(contentView) ? (
          <GqlFullCrudTable<
            IEContentContent,
            | {
                resource?: IEContentResource;
                libraryId?: number;
                libraryName?: string;
                goToContents: () => void;
                goToCreate: () => void;
                goToCreateItem: () => void;
                usedKeywords?: string[];
                refetchTranslationSupport?: () => Promise<
                  ApolloQueryResult<object>
                >;
                floatButton?: boolean;
              }
            | any
          >
            form={{
              title: resolveFormTitle,
              hasPanel: false,
              hasClearStyles: true,
              linkNewTab,
              component: EContentItemContentForm,
              default: {
                itemId: entity?.id,
              },
              props: {
                resource,
                goToContents,
                goToCreate,
                goToCreateItem,
                usedKeywords,
                refetchTranslationSupport,
                floatButton,
              },
            }}
            gql={{
              query: eContentItemContents,
              single: eContentItemContent,
              create: createEContentItemContent,
              update: updateEContentItemContent,
              count: eContentItemContentsCount,
              singleVariables: variables,
              cookQueryVariables,
              refetchOnCrudQueries: refetchSingleQuery,
              gqlFetchPolicy: 'no-cache',
            }}
            list={{
              hasPaginationRouting: false,
              onRowClick: handleRowClick,
              hasGridPagination: true,
              isAllPageOptions: true,
              hasFormActions: true,
              onFilterChange,
              rightActionButtons,
              title: 'Contents',
              cookItems: handleCookItems,
              filterComponent:
                contentViewInitial === ContentViewTypes.LIST_DETAIL.value
                  ? {
                      languageId: FilterBar.LanguagesMultiSelector,
                      view: contentViewSelector,
                      customizeColumn: FilterBar.CustomizeColumn,
                      status: FilterBar.StatusWithDraftMultiSelector,
                      sortOrder: FilterBar.ContentSortingSelector,
                    }
                  : {
                      languageId: FilterBar.LanguagesMultiSelector,
                      view: contentViewSelector,
                      status: FilterBar.StatusWithDraftMultiSelector,
                      sortOrder: FilterBar.ContentSortingSelector,
                    },
              filterComponentProps: {
                languageId: {
                  filterOptions: filterLanguages,
                  title: t('Language'),
                },
                customizeColumn: {
                  itemTitle,
                  name: 'customizeColumn',
                  options: availableColumns,
                  value: selectedColumns,
                  liveSearch: false,
                },
              },
              tableConfig: {
                columns: filteredColumns,
                additionalOptionsList,
                isDraggable: true,
                isDraggableHover: true,
                canDrag,
                onDragSortUpdate,
              },
              initialFilter: {
                count: defaultFilterValues.count,
                status: [Active.value],
                languageId: resource?.languageIds || [],
                sortOrder: contentSorting,
                view: contentView,
                customizeColumn: selectedColumns,
              },
              hasClearStyles: true,
            }}
            panelClassName="no-padding-top"
            renderTitle={renderTitle}
            title="Contents List"
          />
        ) : (
          <GqlFullCrud
            form={{
              title: resolveFormTitle,
              hasPanel: false,
              hasClearStyles: true,
              linkNewTab,
              component: EContentItemContentForm,
              default: {
                itemId: entity?.id,
              },
              props: {
                resource,
                goToContents,
                goToCreate,
                goToCreateItem,
                usedKeywords,
                refetchTranslationSupport,
                floatButton,
              },
            }}
            gql={{
              query: eContentItemContents,
              single: eContentItemContent,
              create: createEContentItemContent,
              update: updateEContentItemContent,
              count: eContentItemContentsCount,
              singleVariables: variables,
              cookQueryVariables,
              refetchOnCrudQueries: refetchSingleQuery,
              gqlFetchPolicy: 'no-cache',
            }}
            list={{
              onRowClick: handleRowClickGrid,
              onFilterChange,
              rightActionButtons,
              card: {
                body: cardBody,
                leftLabel,
                middleLabel,
                hasDeleteButton: false,
                title: cardTitle,
                className: classNames(
                  styles.card,
                  contentView === ContentViewTypes.GRID.value && styles.noSpace,
                ),
                addExtraActions,
              },
              title: 'Contents List',
              panelClassName: 'no-padding-top',
              renderTitle,
              filterComponent:
                contentViewInitial === ContentViewTypes.LIST_DETAIL.value
                  ? {
                      languageId: FilterBar.LanguagesMultiSelector,
                      view: contentViewSelector,
                      customizeColumn: FilterBar.CustomizeColumn,
                      status: FilterBar.StatusWithDraftMultiSelector,
                      sortOrder: FilterBar.ContentSortingSelector,
                    }
                  : {
                      languageId: FilterBar.LanguagesMultiSelector,
                      view: contentViewSelector,
                      status: FilterBar.StatusWithDraftMultiSelector,
                      sortOrder: FilterBar.ContentSortingSelector,
                    },
              filterComponentProps: {
                languageId: {
                  filterOptions: filterLanguages,
                  title: t('Language'),
                },
                customizeColumn: {
                  itemTitle,
                  name: 'customizeColumn',
                  options: availableColumns,
                  value: selectedColumns,
                  liveSearch: false,
                },
              },
              initialFilter: {
                status: [Active.value],
                languageId: resource?.languageIds || [],
                sortOrder: contentSorting,
                view: contentView,
              },
              hasClearStyles: true,
            }}
            title="Contents"
          />
        )}
        <Route exact path={url}>
          <SubmitSection
            isNew={false}
            onDelete={onDelete}
            onGoBack={_onGoBack}
          />
        </Route>
        <Modal
          size="lg"
          title={modalTitle}
          visible={!!contentToMove}
          onClose={handleClose}
        >
          {!fLoading && (
            <ContentMoveModal
              content={contentToMove as IEContentContent}
              libraryId={libraryId}
              libraryName={libraryName}
              resource={resource}
              resourceId={resource?.id}
              onClose={handleClose}
              onMoveContent={handleMoveContent}
            />
          )}
        </Modal>
        <ContentsMoveModal
          countQuery={eContentItemContentsCount}
          itemId={itemId}
          libraryId={libraryId}
          libraryName={libraryName}
          moveEContentItemContent={moveEContentItemContent}
          query={eContentItemContents}
          resource={resource}
          title={t('Move Contents')}
          updateMovedItems={updateMovedItems}
          visible={showMoveContentsModal}
          onClose={closeMoveContentsModal}
        />
      </span>
    </LoadingMask>
  );
};

export default EContentItemContentsTab;
const itemTitle = ({ title }) => title;
interface ITableColumn {
  id: string;
  title?: string;
}
