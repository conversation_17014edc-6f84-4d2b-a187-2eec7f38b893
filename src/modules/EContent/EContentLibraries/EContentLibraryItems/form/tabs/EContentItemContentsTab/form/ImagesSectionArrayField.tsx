import React, { useCallback, useMemo } from 'react';
import { get } from 'lodash';
import classnames from 'classnames';

import useT from '../../../../../../../../common/components/utils/Translations/useT';
import AttachmentFieldWithPreview from '../../../../../../../../common/components/containers/EntityForm/fields/AttachmentFieldWithPreview';
import { IMAGES } from '../../../../../../../../common/propTypes';
import useCurrentUser from '../../../../../../../../common/data/hooks/useCurrentUser';
import { E_CONTENT_CONTENT_FILE } from '../../../../../../../../fsCategories';
import { OPTIONAL, TRuleType } from '../../../../../../../../model/RuleType';
import AttachmentItemCard from '../../../../../../../../common/components/controls/Attachments/listItems/AttachmentItemCard';
import useEntityFormContext from '../../../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import styles from './EContentItemContentForm.scss';
import { IEContentResourceCategory } from '../../../../../../../../common/abstract/EContent/IEContentResourceXAttribute';

const BYTES_IN_KILOBYTE = 1024;
const FIELD_NAME = 'cookedClobs.image';

export interface IImagesSectionArrayField {
  minNumberOfImages: number;
  maxNumberOfImages: number;
  maxSizeOfEachImage: number;
  imageCaptionRule?: TRuleType;
  hasCaption: boolean;
  maxTagLength?: number;
  isKeywordRequired?: boolean;
  hasKeyword?: boolean;
  suggestions?: string[];
  isImageAiImprovementEnabled: boolean;
  isContentCategoryEnabled: boolean;
  resourceCategoriesOptions: IEContentResourceCategory[];
  selectedTabId?: number | string;
}

const ImagesSectionArrayField: React.FC<IImagesSectionArrayField> = ({
  minNumberOfImages,
  maxNumberOfImages,
  maxSizeOfEachImage,
  imageCaptionRule = OPTIONAL.value,
  hasCaption = false,
  maxTagLength,
  isKeywordRequired,
  hasKeyword,
  suggestions,
  isImageAiImprovementEnabled,
  isContentCategoryEnabled,
  resourceCategoriesOptions,
  selectedTabId,
}) => {
  const t = useT();

  const maxSizeInBytes = useMemo(() => maxSizeOfEachImage * BYTES_IN_KILOBYTE, [
    maxSizeOfEachImage,
  ]);

  const {
    me: { tenantId, organisationGroupId },
  } = useCurrentUser();

  const attachmentItemComponent = useCallback(
    props => (
      <AttachmentItemCard
        hasSequence
        documentCaptionRule={imageCaptionRule}
        hasCaption={hasCaption}
        hasKeyword={hasKeyword}
        isContentCategoryEnabled={isContentCategoryEnabled}
        isImageAiImprovementEnabled={isImageAiImprovementEnabled}
        isKeywordRequired={isKeywordRequired}
        maxTagLength={maxTagLength}
        resourceCategoriesOptions={resourceCategoriesOptions}
        selectedTabId={selectedTabId}
        suggestions={suggestions}
        {...props}
      />
    ),
    [
      isContentCategoryEnabled,
      resourceCategoriesOptions,
      hasCaption,
      imageCaptionRule,
      maxTagLength,
      isKeywordRequired,
      hasKeyword,
      suggestions,
      isImageAiImprovementEnabled,
      selectedTabId,
    ],
  );

  const { values } = useEntityFormContext();

  const currentNumber = useMemo(() => get(values, `${FIELD_NAME}.length`, 0), [
    values,
  ]);

  const label = useMemo(() => {
    let heading = 'Images';
    if (maxNumberOfImages > 1) {
      heading = `${heading} (${currentNumber} out of ${maxNumberOfImages})`;
    }
    return heading;
  }, [maxNumberOfImages, currentNumber]);

  return (
    <div className={classnames('row', styles.attachmentsGrid)}>
      <span id="image-section-anchor" />
      <AttachmentFieldWithPreview
        isDownloadable
        isSubsection
        addButtonTitle={t('Add Image')}
        allowedFileTypes={IMAGES}
        allowedNumberOfFiles={maxNumberOfImages}
        attachmentItemComponent={attachmentItemComponent}
        cardsInRow={3}
        categoryKey={E_CONTENT_CONTENT_FILE}
        columns={1}
        hasErrorMessage={false}
        hasRoutes={false}
        maxDescriptionLength={400}
        maxSize={maxSizeInBytes}
        name="cookedClobs.image"
        organisationGroupId={organisationGroupId}
        required={minNumberOfImages > 0}
        strikeTitle={t(label)}
        tenantId={tenantId}
      />
    </div>
  );
};

export default ImagesSectionArrayField;
