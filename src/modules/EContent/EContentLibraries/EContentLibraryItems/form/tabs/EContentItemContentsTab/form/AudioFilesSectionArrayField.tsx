import React, { useCallback, useMemo } from 'react';
import { get } from 'lodash';
import classnames from 'classnames';

import useT from '../../../../../../../../common/components/utils/Translations/useT';
import AttachmentFieldWithPreview from '../../../../../../../../common/components/containers/EntityForm/fields/AttachmentFieldWithPreview';
import { AUDIO } from '../../../../../../../../common/propTypes';
import useCurrentUser from '../../../../../../../../common/data/hooks/useCurrentUser';
import { E_CONTENT_CONTENT_FILE } from '../../../../../../../../fsCategories';
import { OPTIONAL, TRuleType } from '../../../../../../../../model/RuleType';
import useEntityFormContext from '../../../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import styles from './EContentItemContentForm.scss';
import AttachmentItemCard from '../../../../../../../../common/components/controls/Attachments/listItems/AttachmentItemCard';
import { AudioProvider } from '../../../../../../../../common/components/controls/UploadFileDialog/AudioContext';
import { IEContentResourceCategory } from '../../../../../../../../common/abstract/EContent/IEContentResourceXAttribute';

const BYTES_IN_MEGABYTE = 1048576;
const FIELD_NAME = 'cookedClobs.audioFile';

export interface IAudioFilesSectionArrayField {
  minNumberOfAudioFiles: number;
  maxNumberOfAudioFiles: number;
  maxSizeOfEachAudioFile: number;
  audioFileCaptionRule?: TRuleType;
  hasCaption: boolean;
  maxTagLength?: number;
  isKeywordRequired?: boolean;
  hasKeyword?: boolean;
  suggestions?: string[];
  isTextToAudioEnabled?: boolean;
  audioValidation?: boolean;
  audioValidationParameters?: {
    pitch: number;
    frequency: number;
    noise: number;
    loudness: number;
  };
  isContentCategoryEnabled: boolean;
  resourceCategoriesOptions: IEContentResourceCategory[];
  selectedTabId?: number | string;
}

const AudioFilesSectionArrayField: React.FC<IAudioFilesSectionArrayField> = ({
  minNumberOfAudioFiles,
  maxNumberOfAudioFiles,
  maxSizeOfEachAudioFile,
  audioFileCaptionRule = OPTIONAL.value,
  hasCaption = false,
  maxTagLength,
  isKeywordRequired,
  hasKeyword,
  suggestions,
  isTextToAudioEnabled,
  audioValidation = false,
  audioValidationParameters,
  isContentCategoryEnabled,
  resourceCategoriesOptions,
  selectedTabId,
}) => {
  const t = useT();

  const maxSizeInBytes = useMemo(
    () => maxSizeOfEachAudioFile * BYTES_IN_MEGABYTE,
    [maxSizeOfEachAudioFile],
  );

  const {
    me: { tenantId, organisationGroupId },
  } = useCurrentUser();

  const attachmentItemComponent = useCallback(
    props => (
      <AttachmentItemCard
        hasSequence
        documentCaptionRule={audioFileCaptionRule}
        hasCaption={hasCaption}
        hasKeyword={hasKeyword}
        isContentCategoryEnabled={isContentCategoryEnabled}
        isKeywordRequired={isKeywordRequired}
        maxTagLength={maxTagLength}
        resourceCategoriesOptions={resourceCategoriesOptions}
        selectedTabId={selectedTabId}
        suggestions={suggestions}
        {...props}
      />
    ),
    [
      isContentCategoryEnabled,
      resourceCategoriesOptions,
      hasCaption,
      audioFileCaptionRule,
      maxTagLength,
      isKeywordRequired,
      hasKeyword,
      suggestions,
      selectedTabId,
    ],
  );

  const { values } = useEntityFormContext();

  const currentNumber = useMemo(() => get(values, `${FIELD_NAME}.length`, 0), [
    values,
  ]);

  const label = useMemo(() => {
    let heading = 'Audio Files';
    if (maxNumberOfAudioFiles > 1) {
      heading = `${heading} (${currentNumber} out of ${maxNumberOfAudioFiles})`;
    }
    return heading;
  }, [maxNumberOfAudioFiles, currentNumber]);

  return (
    <AudioProvider>
      <div className={classnames('row', styles.attachmentsGrid)}>
        <AttachmentFieldWithPreview
          isDownloadable
          isSubsection
          addButtonTitle={t('Add Audio File')}
          allowedFileTypes={AUDIO}
          allowedNumberOfFiles={maxNumberOfAudioFiles}
          attachmentItemComponent={attachmentItemComponent}
          audioValidation={audioValidation}
          audioValidationParameters={audioValidationParameters}
          cardsInRow={3}
          categoryKey={E_CONTENT_CONTENT_FILE}
          columns={1}
          hasErrorMessage={false}
          hasPhotoRecorder={false}
          hasRoutes={false}
          isTextToAudioEnabled={isTextToAudioEnabled}
          maxDescriptionLength={400}
          maxSize={maxSizeInBytes}
          name={FIELD_NAME}
          organisationGroupId={organisationGroupId}
          required={minNumberOfAudioFiles > 0}
          strikeTitle={t(label)}
          tenantId={tenantId}
        />
      </div>
    </AudioProvider>
  );
};

export default AudioFilesSectionArrayField;
