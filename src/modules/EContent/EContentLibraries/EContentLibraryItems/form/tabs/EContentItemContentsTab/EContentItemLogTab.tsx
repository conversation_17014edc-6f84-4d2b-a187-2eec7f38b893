import React, { useC<PERSON>back, useContext, useMemo, useState } from 'react';
import moment from 'moment';
import { isEqual, map } from 'lodash';
import { useMutation } from 'react-apollo';
import classNames from 'classnames';

import createEContentLog from '../../../../../../../../src/common/data/eContent/createEContentLog.graphql';
import updateEContentLog from '../../../../../../../../src/common/data/eContent/updateEContentLog.graphql';
import deleteEContentLog from '../../../../../../../../src/common/data/eContent/deleteEContentLog.graphql';
import eContentLogsGql from '../../../../../../../../src/common/data/eContent/eContentLogs.graphql';
import eContentLogsCountGql from '../../../../../../../../src/common/data/eContent/eContentLogsCount.graphql';
import useT from '../../../../../../../common/components/utils/Translations/useT';
import GqlFullCrudTable from '../../../../../../../common/components/dataViews/GqlFullCrudTable';
import EContentLogSelector from '../../../../../../../common/components/controls/FilterBar/EContentLogSelector';
import EContentLogDateRangeSelector from '../../../../../../../common/components/controls/FilterBar/EContentLogDateRangeSelector';
import EContentLogType, {
  ManualLog,
} from '../../../../../../../model/EContentLogType';
import {
  format,
  TransportFormat,
} from '../../../../../../../common/utils/dateTime';
import { AddButton } from '../../../../../../../common/components/controls/PanelButtons';
import LogModal from './form/EContentLog/EContentItemLogModal';
import columns from './EContentLogTableColumns';
import useFilterStore from '../../../../../../../common/components/controls/FilterBar/hooks/useFilterStore';
import swal2 from '../../../../../../../common/utils/swal2';
import styles from './EContentItemLogTab.scss';
import EditSessionContext from '../../../../../../../common/components/containers/EditSessionProvider/EditSessionContext';
import LoadingMask from '../../../../../../../common/components/utils/LoadingMask';
import Notifications from '../../../../../../../common/utils/Notifications';

const DEFAULT_LAST_DAYS = 30;

const EContentItemLogTab = ({
  contentId,
}: {
  contentId: number | undefined;
}) => {
  const t = useT();
  const _columns = useMemo(() => columns(t), [t]);
  const [selectedItem, setSelectedItem] = useState<IEContentItemLog | null>(
    null,
  );
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [counter, setCounter] = useState<number>(0);
  const { loading: sloading, isEditSessionActive } = useContext(
    EditSessionContext,
  );

  const initialLogDates = useMemo(() => {
    const now = moment();
    return {
      startDate: format(
        moment(now).subtract(DEFAULT_LAST_DAYS, 'days'),
        TransportFormat,
      ),
      endDate: format(moment(now), TransportFormat),
    };
  }, []);

  const {
    loading: fLoading,
    values: defaultFilterValues,
    onChange: onDefaultFilterValuesChange,
  } = useFilterStore('ECONTENT_LOGS_FILTER', {
    defaultValues: {
      logTypeIds: map(EContentLogType.Basic, 'id'),
      sortKey: 'created_at',
      sortOrder: 'desc',
    },
  });

  const handleRowClick = useCallback(
    (item: IEContentItemLog) => {
      if (
        item?.logTypeId !== ManualLog.id ||
        sloading ||
        !isEditSessionActive
      ) {
        return;
      }
      setSelectedItem(item);
      setIsModalVisible(true);
    },
    [sloading, isEditSessionActive],
  );

  const [_createEContentItemLog] = useMutation(createEContentLog);
  const [_updateEContentItemLog] = useMutation(updateEContentLog);
  const [_deleteEContentItemLog] = useMutation(deleteEContentLog);

  const handleSubmit = useCallback(
    async (entity: IEContentItemLog) => {
      await _createEContentItemLog({
        variables: {
          params: {
            contentId,
            description: entity.description,
            logTypeId: ManualLog.id,
          },
        },
      });

      setCounter(prev => prev + 1);
      setIsModalVisible(false);
    },
    [_createEContentItemLog, contentId],
  );

  const handleUpdate = useCallback(
    async (entity: IEContentItemLog) => {
      await _updateEContentItemLog({
        variables: {
          id: entity.id,
          params: {
            contentId,
            description: entity.description,
            logTypeId: entity.logTypeId,
          },
        },
      });

      setCounter(prev => prev + 1);
      setIsModalVisible(false);
    },
    [_updateEContentItemLog, contentId],
  );

  const handleDelete = useCallback(
    async (entity: IEContentItemLog) => {
      if (entity) {
        const { isConfirmed } = await swal2({
          title: t('Remove'),
          text: t('Are you sure you want to remove the log?'),
          icon: 'warning',
          deleteMode: true,
          className: classNames(styles.modalWrapper),
          confirmButtonText: t('Remove'),
          cancelButtonText: t('Cancel'),
        });

        if (!isConfirmed) {
          return;
        }

        await _deleteEContentItemLog({
          variables: {
            id: entity.id,
          },
        });
        Notifications.success(t('Removed Successfully'), '', t);
      }

      setCounter(prev => prev + 1);
      setIsModalVisible(false);
    },
    [_deleteEContentItemLog, t],
  );

  const handleAddButtonClick = () => {
    setSelectedItem(null);
    setIsModalVisible(true);
  };

  const rightActionButtons = useMemo(
    () => (
      <AddButton
        isSingle
        title={t('Add Manual Log')}
        onClick={handleAddButtonClick}
      />
    ),
    [t],
  );

  const onFilterChange = useCallback(
    filters => {
      if (!isEqual(filters, defaultFilterValues)) {
        onDefaultFilterValuesChange({
          ...filters,
          startDate: filters?.logDates?.startDate,
          endDate: filters?.logDates?.endDate,
        });
      }
    },
    [onDefaultFilterValuesChange, defaultFilterValues],
  );

  const isListItemQueryPrevented = useCallback(
    variables => fLoading || !variables.startDate || !variables.endDate,
    [fLoading],
  );

  return (
    <LoadingMask active={fLoading || sloading}>
      <GqlFullCrudTable
        hasFilters
        hasPagination
        gql={{
          query: eContentLogsGql,
          count: eContentLogsCountGql,
          queryVariables: {
            contentId,
            counter,
          },
          gqlFetchPolicy: 'no-cache',
          isListItemQueryPrevented,
        }}
        list={{
          hasClearStyles: true,
          onFilterChange,
          rightActionButtons,
          onRowClick: handleRowClick,
          hasPaginationRouting: false,
          filterComponent: {
            logDates: EContentLogDateRangeSelector,
            logTypeIds: EContentLogSelector,
          },
          initialFilter: {
            ...defaultFilterValues,
            logDates: initialLogDates,
          },
          tableConfig: {
            columns: _columns,
          },
        }}
        title="Audit Log"
      />
      <LogModal
        key="econtent-item-audit-log"
        isModalVisible={isModalVisible}
        isNew={!selectedItem?.id}
        item={selectedItem}
        setModalVisibility={setIsModalVisible}
        onDelete={handleDelete}
        onSubmit={handleSubmit}
        onUpdate={handleUpdate}
      />
    </LoadingMask>
  );
};

export default EContentItemLogTab;

export interface IEContentItemLog {
  id?: number;
  tenantId?: number;
  personId?: number;
  logTypeId: number;
  description: string;
  createdAt?: Date;
}

export interface ICreateEContentItemLogInput {
  discription: string;
}
