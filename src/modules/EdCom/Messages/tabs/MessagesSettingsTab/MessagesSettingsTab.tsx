import { endsWith, head, isEqual, map, pick, reduce } from 'lodash';
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import IRecipientMinimal from '../../../../../common/abstract/EdCom/IRecipientMinimal';
import { IFileAttachmentTemp } from '../../../../../common/abstract/IFileAttachments';
import IMessage from '../../../../../common/abstract/Messages/IMessage';
import EntityForm from '../../../../../common/components/containers/EntityForm';
import { IBasicEntity } from '../../../../../common/components/containers/EntityForm/EntityForm';
import EntityFormFieldSet from '../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import AttachFileField from '../../../../../common/components/containers/EntityForm/fields/AttachFileField';
import EntityNameField from '../../../../../common/components/containers/EntityForm/fields/EntityNameField';
import useIsMessageMine from '../../../../../common/components/forms/MessageItems/hooks/useIsMessageMine';
import useIsMessageReadonly from '../../../../../common/components/forms/MessageItems/hooks/useIsMessageReadonly';
import useMessageDefaultIcon from '../../../../../common/components/forms/MessageItems/hooks/useMessageDefaultIcon';
import SpinnerError from '../../../../../common/components/utils/SpinnerError';
import useUserPreferences from '../../../../../common/data/hooks/useUserPreferences';
import { ED_COM_CHAT_ATTACHMENTS } from '../../../../../fsCategories';
import { isPrivateChat } from '../../../../../model/MessageType';
import EdComOrganisationSelectorField, {
  COMBINED_ID,
  getCombinedIDValue,
} from '../../../common/EdComOrganisationSelectorField';
import useEdComRightForm from '../../../EdComCrud/context/EdComRightForm';
import useEdComSelectedItem from '../../../EdComCrud/context/EdComSelectedItem';
import useEdComTabs from '../../../EdComCrud/context/EdComTabs';
import useEdComTempItem from '../../../EdComCrud/context/EdComTempItem';
import IMessageSettingsInput from '../../abstract/IMessageSettingsInput';
import IMessageTemp from '../../abstract/IMessageTemp';
import useDeleteOrLeftChat from '../hooks/useDeleteOrLeftChat';
import { STAFF } from '../../../../../model/PersonEntityType';
import { useMobileDetection } from '../../../../EContent/EContentLibraries/EContentLibraryItems/form/tabs/EContentItemContentsTab/form/Question/QuestionSetOption';

const MAX_NAME_LENGTH = 140;
const MessagesSettingsTab: FC = () => {
  const {
    selectedItem,
    setUserPrefferences,
  } = useEdComSelectedItem<IMessage>();
  const { tempData } = useEdComTempItem<IMessageTemp>();
  const {
    onSubmit,
    onCancel,
    onGoNext,
  } = useEdComRightForm<IMessageSettingsInput>();
  const isMobile = useMobileDetection();
  const { button, loading } = useDeleteOrLeftChat();
  const isMessageReadonly = useIsMessageReadonly();
  const isNew = useMemo<boolean>(() => !selectedItem?.id, [selectedItem]);
  const isMessageMine = useIsMessageMine();
  const defaultIcon = useMessageDefaultIcon(selectedItem);

  const isReadOnly = useMemo<boolean>(
    () =>
      isMessageReadonly ||
      isPrivateChat(selectedItem) ||
      !isMessageMine(selectedItem),
    [selectedItem, isMessageMine, isMessageReadonly],
  );

  const {
    preferences: { ED_COM_MESSAGES_ORGANISATION: preferences } = {},
  } = useUserPreferences();

  const _entity = useMemo<IMessagesSettingsTabEntity>(
    () =>
      selectedItem?.recipientPerson && isPrivateChat(selectedItem)
        ? {
            id: selectedItem.id,
            name: `${selectedItem.recipientPerson.firstName} ${selectedItem.recipientPerson.lastName}`,
            thumbnail: {
              fileId: selectedItem.recipientPerson.photoFileId,
            } as IFileAttachmentTemp,
            personEntityAllocationId: selectedItem.personEntityAllocationId,
            organisationId: selectedItem.organisationId,
            orgStructureLevelId: selectedItem.orgStructureLevelId,
            [COMBINED_ID]: getCombinedIDValue(selectedItem),
          }
        : {
            id: selectedItem?.id || undefined,
            name:
              selectedItem?.name || defaultName(tempData.participants) || '',
            thumbnail: selectedItem?.thumbnail,
            personEntityAllocationId:
              selectedItem?.personEntityAllocationId ||
              preferences?.personEntityAllocationId,
            organisationId:
              selectedItem?.organisationId || preferences?.organisationId,
            orgStructureLevelId:
              selectedItem?.orgStructureLevelId || preferences?.organisationId,
            [COMBINED_ID]: getCombinedIDValue(selectedItem),
          },
    [preferences, selectedItem, tempData.participants],
  );

  const _isSubmitDisabled = useCallback(
    (isDisabled: boolean, formState) => {
      const { dirty, isSubmitting } = formState;
      return (isReadOnly || !isNew) && !dirty && !isSubmitting;
    },

    [isReadOnly, isNew],
  );

  const _onSubmit = useCallback<(values: IMessagesSettingsTabEntity) => void>(
    async ({
      thumbnail,
      name,
      organisationId,
      personEntityAllocationId,
      orgStructureLevelId,
    }) => {
      if (
        onSubmit &&
        selectedItem?.type &&
        personEntityAllocationId &&
        !isMessageReadonly
      ) {
        await setUserPrefferences({
          personEntityAllocationId,
          organisationId,
          orgStructureLevelId,
        });
        return onSubmit({
          participantsAdded:
            tempData.participants
              ?.filter(({ id }) => typeof id === 'string')
              .map(({ id }) => id) || [],
          messageId: selectedItem?.id,
          type: selectedItem?.type,
          name,
          thumbnail: pick(thumbnail, [
            'fileId',
            'uploadToken',
            'description',
            'fileName',
          ]),
          isThumbnailUpdated:
            !!selectedItem.id &&
            (selectedItem.thumbnail?.fileId !== thumbnail?.fileId ||
              selectedItem.thumbnail?.version !== thumbnail?.version),
          personEntityAllocationId,
          organisationId,
          orgStructureLevelId,
        });
      }
    },
    [tempData.participants, onSubmit, selectedItem, isMessageReadonly],
  );

  const { tabs } = useEdComTabs<IMessage>();
  const [_tabs, _setTabs] = useState(tabs);

  useEffect(() => {
    if (selectedItem?.id && !isEqual(map(_tabs, 'route'), map(tabs, 'route'))) {
      onGoNext();
    }
    _setTabs(tabs);
  }, [selectedItem, tabs, _tabs, onGoNext]);

  const organisationName =
    selectedItem?.organisationAllocationNames?.organisationName;
  const allocationsNames =
    selectedItem?.organisationAllocationNames?.allocationsNames || [];

  const _isEdcomSelectorDisabled = useMemo(
    () => selectedItem?.isMessageDisabled || isReadOnly || !isNew,
    [selectedItem, isReadOnly, isNew],
  );

  return (
    <SpinnerError firstLoading={false} loading={loading}>
      <EntityForm<IMessagesSettingsTabEntity>
        hasLeavePrompt
        entity={_entity}
        isNew={isNew}
        isReadOnly={isReadOnly}
        isSubmitDisabled={_isSubmitDisabled}
        leftBottomSection={button}
        onCancel={onCancel}
        onSubmit={_onSubmit}
      >
        <EntityFormFieldSet>
          <AttachFileField
            columns={isMobile ? 0 : -1}
            defaultIcon={defaultIcon}
            fileCategory={ED_COM_CHAT_ATTACHMENTS}
            name="thumbnail"
          />
          <EntityNameField maxLength={MAX_NAME_LENGTH} />
          <EdComOrganisationSelectorField
            hasStructureLevel
            allocationsNames={allocationsNames}
            gqlFetchPolicy="cache-first"
            isDisabled={_isEdcomSelectorDisabled}
            isRequired={
              selectedItem?.personEntityAllocationId &&
              selectedItem?.organisationId
                ? false
                : true
            }
            name={COMBINED_ID}
            orgName={organisationName}
          />
        </EntityFormFieldSet>
      </EntityForm>
    </SpinnerError>
  );
};

export default MessagesSettingsTab;

interface IMessagesSettingsTabEntity extends IBasicEntity {
  name: string;
  thumbnail?: IFileAttachmentTemp;
  personEntityAllocationId?: number;
  organisationId?: number;
  orgStructureLevelId?: number;
}

const defaultName = (participants?: IRecipientMinimal[]): string =>
  reduce<IRecipientMinimal, string>(
    participants || [],
    (acc, { firstName, lastName }, index) => {
      const newPerson = `${index ? ', ' : ''}${firstName} ${head(lastName)}`;

      if (endsWith(acc, SPREAD)) {
        return acc;
      }

      if (
        acc.length < MAX_NAME_LENGTH &&
        ((participants?.length === index + 1 &&
          acc.length + newPerson.length < MAX_NAME_LENGTH) ||
          acc.length + newPerson.length + SPREAD.length < MAX_NAME_LENGTH)
      ) {
        acc += newPerson;
      } else {
        acc += SPREAD;
      }

      return acc;
    },
    '',
  );

const SPREAD = ',...';
