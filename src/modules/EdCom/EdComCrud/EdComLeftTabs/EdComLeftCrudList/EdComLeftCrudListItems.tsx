import { map, orderBy } from 'lodash';
import React, { PropsWithChildren, useState, useMemo } from 'react';
import Empty from '../../../../../common/components/utils/Empty';
import IWithId from '../../abstract/IWithId';
import styles from './EdComLeftCrudList.scss';
import EdComLeftCrudListItemComponent from './EdComLeftCrudListItemComponent';

export default function EdComLeftCrudListItems<TEntity extends IWithId>({
  items,
  handleDelete,
}: Readonly<PropsWithChildren<IEdComLeftCrudListItemsProps<TEntity>>>) {
  const [itemLocal, setItemLocal] = useState<TEntity>();

  const sortedItems = useMemo(() => {
    if (!items?.length) return items;

    const hasUpdatedAt =
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      items[0] && 'updatedAt' in items[0] && items[0].updatedAt;
    if (hasUpdatedAt) {
      return orderBy(items, ['updatedAt'], ['desc']);
    }

    return items;
  }, [items]);

  return (
    <div>
      {sortedItems?.length ? (
        map(sortedItems, item => (
          <EdComLeftCrudListItemComponent<TEntity>
            key={item.id}
            handleDelete={handleDelete}
            item={item}
            itemLocal={itemLocal}
            setItemLocal={setItemLocal}
          />
        ))
      ) : (
        <Empty className={styles.section} />
      )}
    </div>
  );
}

export interface IEdComLeftCrudListItemsProps<T> {
  items: T[];
  handleDelete?: (item: T) => Promise<boolean>;
}
