import classnames from 'classnames';
import React, { useCallback, useEffect, useState } from 'react';
import SearchField from '../../../../../common/components/controls/base/SearchField';
import FilterBar from '../../../../../common/components/controls/FilterBar';
import itemIdValue from '../../../../../common/itemIdValue';
import IWithSearchField from '../../../../../common/abstract/EdCom/IWithSearchField';
import useEdComFilterBar from '../../context/EdComFilterBar';
import EdComLeftCrudFilterBar from './EdComLeftCrudFilterBar';
import styles from './EdComLeftCrudFilters.scss';
import listStyles from './EdComLeftCrudList.scss';

export default function EdComLeftCrudFilters<
  TFilter extends IWithSearchField & Record<string, any>
>() {
  const { filter, setFilter } = useEdComFilterBar<TFilter>();
  const [searchQuery, setSearchQuery] = useState<string | null>(null);
  const [category, setCategory] = useState<TFilter[string] | undefined>();

  const [localFilters, setLocalFilters] = useState<Partial<TFilter>>(filter);
  const {
    searchModeName = 'searchQueryMode',
    hasSearchMode = false,
    searchName = 'searchQuery',
    searchModeOptions = [],
  } = useEdComFilterBar<TFilter>();

  useEffect(() => {
    if (searchQuery?.length === 0 || searchQuery?.length || category?.length) {
      triggerSearch();
    }
  }, [category, searchQuery]);

  const _onChange = useCallback(
    (name: keyof TFilter, value) => {
      setLocalFilters({
        ...localFilters,
        [name]: value,
      });
      if (name === 'notificationCategories' || name === 'newsCategories') {
        setCategory(value);
      }
    },
    [localFilters],
  );

  const _onSearchChange = value => setSearchQuery(value || '');

  const _onSearchModeChange = useCallback(
    (value?: string[]) => {
      const updatedFilters = {
        ...localFilters,
        [searchModeName]: value,
      } as TFilter;
      setLocalFilters(updatedFilters);
      // Trigger search immediately when search mode changes
      setFilter({
        ...updatedFilters,
        [searchName]: searchQuery,
      } as TFilter);
    },
    [localFilters, searchModeName, setFilter, searchName, searchQuery],
  );

  const triggerSearch = useCallback(() => {
    setFilter({
      ...localFilters,
      [searchName]: searchQuery,
    } as TFilter);
  }, [localFilters, searchQuery, setFilter]);

  return (
    <>
      <EdComLeftCrudFilterBar
        localFilters={localFilters}
        triggerSearch={triggerSearch}
        onChange={_onChange}
      />

      <div className={styles.searchBoxContainer}>
        <div
          className={classnames(
            !hasSearchMode ? styles.searchFieldGrowth : styles.searchField,
          )}
        >
          <SearchField
            className={classnames(listStyles.section, styles.item)}
            hasBottomMargin={false}
            inputClassName={classnames(
              listStyles.noBorder,
              hasSearchMode ? styles.inputSpacingConv : '',
            )}
            value={searchQuery as string}
            widthClassName="col-sm-12"
            onChange={_onSearchChange}
          />
        </div>
        {hasSearchMode && (
          <FilterBar.Multiselect<string>
            isFullSizeButton
            showSelectAll
            buttonClassName={styles.searchModeButton}
            classNames={styles.searchMode}
            itemId={itemIdValue}
            label="Search Type"
            name={searchModeName}
            options={searchModeOptions}
            value={localFilters[searchModeName]}
            onChange={_onSearchModeChange}
          />
        )}
      </div>
    </>
  );
}
