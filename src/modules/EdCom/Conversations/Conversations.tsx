import React, {
  FC,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { isEqual, map } from 'lodash';
import moment from 'moment';
import { useLocation } from 'react-router-dom';

import EdComCrud from '../EdComCrud';

import HistoryFilter, { getLastMessageFrom } from '../common/HistoryFilter';
import ConversationCard from './card/ConversationCard';
import ConversationsParticipantsTab from './tabs/ConversationsParticipantsTab';
import ConversationsTab from './tabs/ConversationsTab';
import Settings from './tabs/ConversationSettingsTab';
import ConversationAttachments from './tabs/ConversationsAttachments';
import ConversationsNotes from './tabs/ConversationsNotes';
import ConversationsStarredTab from './tabs/ConversationsStarredTab';

import conversationGql from './data/conversation.graphql';
import conversationsGql from './data/conversations.graphql';
import conversationsCountGql from './data/conversationsCount.graphql';
import deleteConversationGql from './data/deleteConversation.graphql';
import createConversationGql from './data/createConversation.graphql';
import updateConversationGql from './data/updateConversation.graphql';

import IConversationFilters from './abstract/IConversationFilters';
import IConversationListVariables from './abstract/IConversationListVariables';
import IConversation from './abstract/IConversation';
import { IModuleComponentProps } from '../../../common/abstract/IModuleProps';

import { OrganisationsFilterContext } from '../../../common/components/containers/OrganisationsFilterContextProvider';
import useT from '../../../common/components/utils/Translations/useT';
import useCurrentUser from '../../../common/data/hooks/useCurrentUser';

import { LAST_7_DAYS } from '../Messages/model/MessageHistory';
import { DELETED, ACTIVE } from './model/ConversationStatus';

import SearchQueryMode from './model/SearchQueryMode';
import EdComEntityFilter from '../EdComCrud/EdComLeftTabs/filters/EdComEntityFilter';
import usePersonEntityTypeGroupSelector from '../../../common/data/hooks/usePersonEntityTypeGroupSelector';

import ConversationContactTypeFilter from './filters/ConversationContactTypeFilter';
import ConversationContactType from './model/ConversationContactType';
import IConversationContactFilters from './abstract/IConversationContactFilters';

import conversationContactsGql from './data/conversationContacts.graphql';
import conversationContactsCountGql from './data/conversationContactsCount.graphql';
import IEdComRightEditTab from '../EdComCrud/abstract/IEdComRightEditTab';
import { ONE_TO_ONE } from './model/ConversationTypePartCount';
import SpinnerError from '../../../common/components/utils/SpinnerError';
import useEnumerableModel from '../../../common/data/hooks/useEnumerableModel';
import ConversationsWrapper from './ConversationsWrapper';
import useFilterStore from '../../../common/components/controls/FilterBar/hooks/useFilterStore';
import Spinner from '../../../common/components/utils/Spinner';
import UnreadConversationCountHook from '../../../common/components/other/UnreadConversationCount/UnreadConversationCountHook';
import UnreadConversationNotesCountHook from '../../../common/components/other/UnreadConversationNotesCount/UnreadConversationNotesCountHook';

const FILTER_KEY = 'CONVERSATIONS_LIST';
const FILTER_KEY_CONTACTS = 'CONTACTS_LIST';

const Conversations: FC<IModuleComponentProps> = ({ title }) => {
  const { pathname } = useLocation();
  const conversationId = useMemo(() => pathname.split('/')[3], [pathname]);
  const hasConversationId = useMemo(() => !isNaN(Number(conversationId)), [
    conversationId,
  ]);

  const { selectedOrganisations } = useContext(OrganisationsFilterContext);
  const t = useT();
  const {
    loading: uLoading,
    error: uError,
    me: { id: userId },
    me,
  } = useCurrentUser();

  const {
    values: defaultFilterValues = {
      searchQuery: '',
      searchQueryMode: SearchQueryMode.SelectAll,
      history: LAST_7_DAYS.value,
    },
    onChange: onDefaultFilterValuesChange,
  } = useFilterStore(FILTER_KEY, {
    defaultValues: {
      searchQuery: '',
      searchQueryMode: SearchQueryMode.SelectAll,
      history: LAST_7_DAYS.value,
    },
  });

  const [filterInit, setFilterInit] = useState(false);
  const [filter, setFilter] = useState(defaultFilterValues);

  useEffect(() => {
    if (!isEqual(filter, defaultFilterValues) && !filterInit) {
      setFilter({ ...filter, ...defaultFilterValues });
      setFilterInit(true);
    }
  }, [defaultFilterValues, filter, filterInit]);

  const _onFilterChange = useCallback(
    values => {
      setFilter(values);
      onDefaultFilterValuesChange(values);
    },
    [onDefaultFilterValuesChange],
  );

  const cookQueryVariables = useCallback<
    (filters: IConversationFilters) => IConversationListVariables
  >(
    ({ searchQuery, history, searchQueryMode }) => ({
      searchQuery,
      organisationIds: map(selectedOrganisations, 'id'),
      lastActivity: getLastMessageFrom(history, moment()),
      searchQueryMode: searchQueryMode
        ? searchQueryMode
        : SearchQueryMode.SelectAll,
    }),
    [selectedOrganisations],
  );

  const canDelete = useCallback(
    ({ status, ownerPersonId }: IConversation) =>
      ownerPersonId === userId && status !== DELETED.value,
    [userId],
  );

  const { options } = useEnumerableModel(SearchQueryMode);

  const {
    loading,
    personEntityTypeGroupSelector,
  } = usePersonEntityTypeGroupSelector();

  const {
    values: defaultFilterValuesContacts = {
      searchQuery: '',
      contactTypes: ConversationContactType.SelectAll,
      personEntityTypeIds: map(personEntityTypeGroupSelector, 'id'),
    },
    onChange: onDefaultFilterValuesChangeContacts,
  } = useFilterStore(FILTER_KEY_CONTACTS, {
    defaultValues: {
      searchQuery: '',
      contactTypes: ConversationContactType.SelectAll,
      personEntityTypeIds: map(personEntityTypeGroupSelector, 'id'),
    },
  });

  const cookQueryVariablesContacts = useCallback(
    filters => {
      if (!isEqual(defaultFilterValuesContacts, filters)) {
        onDefaultFilterValuesChangeContacts(filters);
      }
      return filters;
    },
    [defaultFilterValuesContacts, onDefaultFilterValuesChangeContacts],
  );
  const tabs = useCallback(
    (item?: IConversation) => {
      const tabs: IEdComRightEditTab<IConversation>[] = [
        {
          route: 'settings',
          title: 'Settings',
          createMutation: createConversationGql,
          updateMutation: updateConversationGql,

          element: <Settings />,
        },
      ];

      // if (item && item.typePartCount !== ONE_TO_ONE.value) {
      tabs.unshift({
        route: 'participants',
        title: 'Participants',
        updateMutation: updateConversationGql,
        element: <ConversationsParticipantsTab />,
        goNextOnSubmit: true,
      });
      // }

      if (hasConversationId) {
        tabs.push({
          route: 'conversations',
          title: 'Conversations',
          rightLabel: (
            <UnreadConversationCountHook conversationId={item?.id as number} />
          ),
          element: <ConversationsTab />,
        });

        tabs.push({
          route: 'shared-files',
          title: 'Shared Files',

          element: <ConversationAttachments />,
        });
        tabs.push({
          route: 'notes',
          title: 'Notes',
          rightLabel: (
            <UnreadConversationNotesCountHook
              conversationId={item?.id as number}
            />
          ),

          element: <ConversationsNotes />,
        });
        tabs.push({
          route: 'starred',
          title: 'Starred',

          element: <ConversationsStarredTab />,
        });
      }
      return tabs;
    },
    [hasConversationId],
  );

  const deleteCheckboxText = useCallback(
    () => t('Delete for other participants as well'),
    [t],
  );

  const deleteTitleText = useCallback(
    () => t('Are you sure you want to delete this Conversation?'),
    [t],
  );

  const cookDeleteVariables = useCallback<
    (item: IConversation, checkboxValue: boolean) => object
  >(
    (item, checkboxValue) => ({ id: item.id, isHardDelete: checkboxValue }),
    [],
  );

  const cookSelectedItem = useCallback(
    person =>
      person
        ? {
            participantsAdded: [person, me],
            oneToOnePerson: person,
            typePartCount: ONE_TO_ONE.value,
            topic: `${person.firstName} ${person.lastName}`,
            status: ACTIVE.value,
          }
        : {},
    [me],
  );

  const getTopic = useCallback(
    (item: IConversation) => item?.topic || t('Edit'),
    [t],
  );

  if (!filterInit) {
    return <Spinner />;
  }

  return (
    <SpinnerError error={uError} loading={uLoading}>
      <EdComCrud<
        IConversation,
        IConversationFilters,
        IConversationListVariables,
        IConversationContactFilters
      >
        contacts={{
          filterBar: {
            defaultValue: {
              searchQuery: '',
              contactTypes:
                defaultFilterValuesContacts.contactTypes ||
                ConversationContactType.SelectAll,
              personEntityTypeIds:
                defaultFilterValuesContacts.personEntityTypeIds ||
                map(personEntityTypeGroupSelector, 'id'),
            },
            components: {
              personEntityTypeIds: EdComEntityFilter,
              contactTypes: ConversationContactTypeFilter,
            },
            loading,
          },
          gql: {
            itemsQuery: conversationContactsGql,
            itemsCountQuery: conversationContactsCountGql,
            // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
            // @ts-ignore
            cookQueryVariables: cookQueryVariablesContacts,
          },
          cookSelectedItem,
        }}
        getName={getTopic}
        gql={{
          cookQueryVariables,
          itemsQuery: conversationsGql,
          itemsCountQuery: conversationsCountGql,
          itemQuery: conversationGql,

          deleteMutation: deleteConversationGql,
          cookDeleteVariables,
        }}
        left={{
          addOptionsTitle: 'Conversation',
          addOptions: {
            title: t('Conversation'),
            defaultEntity: {},
          },
          list: {
            ItemBodyComponent: ConversationCard,
            deleteCheckboxText,
            canDelete,
            deleteTitleText,
          },
          filterBar: {
            hasSearchMode: true,
            searchModeOptions: options,
            components: {
              history: HistoryFilter,
            },
            defaultValue: filter as any,
            onChange: _onFilterChange as any,
          },
          Wrapper: ConversationsWrapper,
        }}
        right={{
          tabs,
        }}
        title={title}
      />
    </SpinnerError>
  );
};

export default Conversations;
