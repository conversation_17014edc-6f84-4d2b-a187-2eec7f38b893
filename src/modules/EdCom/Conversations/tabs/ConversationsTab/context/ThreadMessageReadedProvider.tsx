import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useMutation } from 'react-apollo';
import { debounce, isEmpty, findIndex, filter, groupBy, uniq } from 'lodash';

import useThreadMessageAction from '../hook/useThreadMessageAction';
import IThreadMessageItem from '../abstract/IThreadMessageItem';
import ThreadMessageReadedContext from './ThreadMessageReadedContext';
import useThreadMessagesList from '../hook/useThreadMessagesList';
import { dummyQuery } from '../../../../../../common/data/dummyQuery';

const DEBOUNCE_DELAY = 1000;

const ThreadMessageReadedProvider: FC = ({ children }) => {
  const { readQuery, readQueryResolver } = useThreadMessageAction();
  const [readQueryMutation, { data, error }] = useMutation(
    readQuery || dummyQuery,
    {},
  );

  const [readedItems, setReadedItems] = useState<Set<number>>(new Set());
  const { setItems, parentContext, items } = useThreadMessagesList();

  const readContextValue = useMemo(
    () => ({
      readedItems,
      setReadedItem: (item: IThreadMessageItem) =>
        setReadedItems(itemsSet => new Set(itemsSet).add(item.id)),
      setReadedItems,
    }),
    [readedItems, setReadedItems],
  );

  const readQueryBatch = useCallback(
    async (ids: Set<number>) => {
      // Collect parentIds from conversation items that are being marked as read
      const parentIds = Array.from(ids)
        .map(id => {
          const item = items.find(item => item.id === id);
          return item?.parentId;
        })
        .filter(parentId => parentId !== null && parentId !== undefined);

      const readQueryOptions = {
        variables: {
          params: {
            conversationItemIds: Array.from(ids),
            parentIds: uniq(parentIds),
          },
        },
      };
      setReadedItems(currIdsSet => {
        const nextIdsSet = new Set(currIdsSet);
        Array.from(ids).forEach(id => nextIdsSet.delete(id));
        return nextIdsSet;
      });

      setItems(currItems => {
        let newItems = [...currItems];
        for (const id of ids) {
          const idx = findIndex(newItems, { id });
          newItems = [
            ...newItems.slice(0, idx),
            { ...newItems[idx], isReaded: true },
            ...newItems.slice(idx + 1),
          ];
        }
        const lastReadIdx = findIndex(newItems, { isLastReadItem: true });
        if (lastReadIdx >= 0) {
          newItems = [
            ...newItems.slice(0, lastReadIdx),
            { ...newItems[lastReadIdx], isLastReadItem: false },
            ...newItems.slice(lastReadIdx + 1),
          ];
        }

        if (parentContext) {
          const readedItems = filter(
            newItems,
            ({ id, parentId }) => ids.has(id) && parentId,
          );

          const readedItemsHash = groupBy(readedItems, 'parentId');

          const readedItemsHashKeys = Object.keys(readedItemsHash);

          for (const id of readedItemsHashKeys) {
            const parent = parentContext.getItem(Number(id));
            parentContext.replaceItem({
              ...parent,
              unreadCount: parent?.unreadCount
                ? parent?.unreadCount - readedItemsHash[id].length
                : 0,
            } as IThreadMessageItem);
          }
        }

        return newItems;
      });

      readQuery &&
        (await readQueryMutation(
          readQueryResolver
            ? readQueryResolver(readQueryOptions, ids)
            : readQueryResolver,
        ));
    },
    [
      readQueryMutation,
      setReadedItems,
      setItems,
      readQueryResolver,
      readQuery,
      parentContext,
      items,
    ],
  );

  const readQueryBatchDebounce = useMemo(
    () => debounce(readQueryBatch, DEBOUNCE_DELAY),
    [readQueryBatch],
  );

  useEffect(() => {
    if (!isEmpty(readedItems)) {
      readQueryBatchDebounce(readedItems);
    }
  }, [readedItems, readQueryBatchDebounce]);

  return (
    <ThreadMessageReadedContext.Provider value={readContextValue}>
      {children}
    </ThreadMessageReadedContext.Provider>
  );
};

export default ThreadMessageReadedProvider;
