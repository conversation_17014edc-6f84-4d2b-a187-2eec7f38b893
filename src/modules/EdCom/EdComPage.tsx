import React, { useMemo } from 'react';
import { useQuery } from 'react-apollo';
import { map } from 'lodash';
import BasicModuleLayout from '../../common/components/containers/BasicModuleLayout';
import FeaturedPermissionContext from '../../common/components/utils/FeaturedPermission/FeaturedPermissionContext';
import makeFeaturedPermissionHelpers from '../../common/components/utils/FeaturedPermission/makeFeaturedPermissionHelpers';
import { getLanguage } from '../../common/components/utils/Translations/helpers/makeTranslationHelpers';
import TranslationProvider from '../../common/components/utils/Translations/TranslationProvider';
import useConversationsItemsUnreadCount from '../../common/data/hooks/useConversationsItemsUnreadCount';
import messagesItemsUnreadCountQuery from './Messages/data/messagesItemsUnreadCount.graphql';
import { localization } from './localization';

export default function EdComPage({ module, accessList, preferences }) {
  const { data: { messagesItemsUnreadCount } = {} } = useQuery<{
    messagesItemsUnreadCount?: number;
  }>(messagesItemsUnreadCountQuery);

  const { conversationsItemsUnreadCount } = useConversationsItemsUnreadCount();

  const routesDescription = useMemo(() => {
    const extendingStructure = {
      messages: {
        unread: messagesItemsUnreadCount,
      },
      conversations: {
        unread: conversationsItemsUnreadCount,
      },
    };

    return map(module.routesDescription, struct => ({
      ...struct,
      ...extendingStructure[struct.id],
    }));
  }, [
    module.routesDescription,
    messagesItemsUnreadCount,
    conversationsItemsUnreadCount,
  ]);

  return (
    <TranslationProvider
      language={getLanguage(preferences)}
      locale={localization}
    >
      <FeaturedPermissionContext.Provider
        value={makeFeaturedPermissionHelpers(accessList, module)}
      >
        <BasicModuleLayout
          routesDescription={routesDescription}
          title={module.title}
          wrapContentInPanel={false}
        />
      </FeaturedPermissionContext.Provider>
    </TranslationProvider>
  );
}
